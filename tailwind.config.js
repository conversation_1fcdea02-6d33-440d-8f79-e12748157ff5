const bitcineTheme = require('@bitcine/cinesend-theme/tailwind.config')
const defaultTheme = require('tailwindcss/defaultTheme')

module.exports = {
  ...bitcineTheme,
  darkMode: 'class',
  plugins: [],
  content: [
    './node_modules/@bitcine/cinesend-theme/dist/**/*.{html,js,jsx}',
    './src/**/*.{html,js,jsx}'
  ],
  theme: {
    fontFamily: {
      'header': ['ClashDisplay-Variable', ...defaultTheme.fontFamily.sans],
      'subheader': ['ClashGrotesk-Variable', ...defaultTheme.fontFamily.sans],
      'content': ['Inter', ...defaultTheme.fontFamily.sans]
    },
    extend: {
      colors: {
        ...bitcineTheme.theme.extend.colors,
        primary: {
          100: '#E9F2FF',
          200: '#c5dcfd',
          300: '#4e359a',
          500: '#472F91',
          600: '#3e2a80',
          700: '#2a1a60',
          900: '#00091A'
        }
      }
    }
  }
}

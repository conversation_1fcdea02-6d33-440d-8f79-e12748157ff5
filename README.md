# A Quick and Easy* React FrontEnd for Laravel

## Introduction

This lil' Application tries to simplify interaction with a Laravel API

## Quick Start

```shell
yarn install
yarn dev
```

### Configuration

We'll avoid a bunch of js config files. Use the .env file (so far only one directive). Create an environment file ('.env') and add the below line:

```dotenv
VITE_BACKEND_URL=https://dcdc-api.test
```
### Routing and Pages

Routing, primarily, is in the App.js and individual pages are to be defined in `pages`. Layouts and shared - or even potentially shared components should be split off accordingly in the `components` folder. 

### Fun Things
This Create React application contains a custom `useAuth` React hook, designed to abstract all authentication logic away from your pages. In addition, the hook can be used to access the currently authenticated user:

```js
const Dashboard = () => {
    const { logout, user } = useAuth({ middleware: 'auth' })

    return (
        <>
            <p>{user?.name}</p>
            <button onClick={logout}>Sign out</button>
        </>
    )
}

export default Dashboard
```

This react app is expected to run on the same domain as the API for authentication purposes.

This react app uses axios instead of fetch. It is currently configured to apply the authentication tokens and cookies to all requests.

<sup><sub>* ymmv</sub></sup>

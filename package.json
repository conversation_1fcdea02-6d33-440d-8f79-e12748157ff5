{"name": "dcdc-dashboard", "version": "0.1.0", "private": true, "author": "BitCine Technologies", "keywords": ["react", "react-router", "tailwindcss", "vercel", "axios", "react-hooks", "laravel", "sanctum"], "description": "The DCDC dashboard to monitor DCP deliveries to sites.", "repository": {"type": "git", "url": "https://github.com/BitCine/dcdc-dashboard", "directory": "/"}, "license": "MIT", "dependencies": {"@bitcine/cinesend-theme": "^0.3.72", "@headlessui/react": "^1.5.0", "@tailwindcss/forms": "^0.5.0", "@tanstack/react-query": "^5.62.2", "@testing-library/jest-dom": "^5.16.2", "@testing-library/react": "^12.1.4", "@testing-library/user-event": "^13.5.0", "autoprefixer": "^10.4.2", "axios": "1.7.4", "chart.js": "^4.4.2", "chartjs-adapter-dayjs-4": "^1.0.4", "dayjs": "^1.11.2", "dayjs-plugin-utc": "^0.1.2", "husky": "^9.1.7", "lint-staged": "^15.4.2", "postcss": "^8.4.8", "react": "^19.0.0", "react-chartjs-2": "^5.2.0", "react-dom": "^19.0.0", "react-json-view": "^1.21.3", "react-router-dom": "6", "react-scripts": "5.0.0", "tailwindcss": "^3.0.23", "uuid": "^8.3.2", "web-vitals": "^2.1.4"}, "devDependencies": {"@vitejs/plugin-react": "^2.0.1", "prettier": "3.4.2", "vite": "5.4.6"}, "scripts": {"dev": "vite", "build": "vite build", "lint-staged": "lint-staged", "prepare": "husky"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": "prettier --write"}}
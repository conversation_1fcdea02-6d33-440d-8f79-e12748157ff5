import React from 'react'
import { reducer, initialState } from './reducer'

export const DeliveryContext = React.createContext({
  state: initialState,
  dispatch: () => null
})

export const DeliveryProvider = ({ children }) => {
  const [state, dispatch] = React.useReducer(reducer, initialState)

  return (
    <DeliveryContext.Provider value={[ state, dispatch ]}>
    	{ children }
    </DeliveryContext.Provider>
  )
}
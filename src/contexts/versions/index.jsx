import React from 'react'
import { reducer, initialState } from './reducer'

export const VersionsContext = React.createContext({
  state: initialState,
  dispatch: () => null
})

export const VersionsProvider = ({ children }) => {
  const [state, dispatch] = React.useReducer(reducer, initialState)

  return (
    <VersionsContext.Provider value={[state, dispatch]}>
      {children}
    </VersionsContext.Provider>
  )
}

export const reducer = (state, action) => {
  if (action) {
    if (action.versions) {
      return {
        ...state,
        isValidating: action.isValidating ?? false,
        error: action.error,
        versions: action.versions?.data ?? []
      }
    }
    if (action.transfers?.length) {
      // find any matching transfer in the versions table and update accordingly.
      // if we switch from running to completed within this data, ping the server.
      let versions = state.versions
      action.transfers.forEach((transfer, idx) => {
        let uuid = transfer.transfer_spec.tags.cinesend.transfer_uuid
        let version = versions.find((version, idx) =>
          version.aspera_transfer?.transfer_uuid === uuid
        )
        if (version) {
          if (version.aspera_transfer.status !== 'completed'
            && version.aspera_transfer.status !== 'invalid' ) {
            version.aspera_transfer.status = transfer.status
          }
          if (! version.version_name) {
            version.version_name = transfer.transfer_spec.paths[0].source.split('/').slice(-1).pop()
          }
          version.aspera_transfer.aspera_statistics.bytes_written = transfer.bytes_written
          version.aspera_transfer.aspera_statistics.bytes_expected = transfer.bytes_expected
          // re-inject the modified version into the state obj.
          versions.map(v => (v.aspera_transfer?.transfer_uuid === uuid ? v : version));
        }
      })
      return {
        ...state,
        error: action.error,
        versions: versions,
        transfers: action.transfers ?? []
      }
    }
  }
  return state
}

export const initialState = {
  isValidating: true,
  error: false,
  versions: [],
  transfers: []
}

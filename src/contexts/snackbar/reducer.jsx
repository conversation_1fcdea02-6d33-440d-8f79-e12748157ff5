export const reducer = (state, action) => {
  switch (action.type) {
    case 'add_message':
      return {
        ...state,
        messages: [...state.messages, action.message]
      }
    case 'remove_message':
      return {
        ...state,
        messages: state.messages.filter(message => message._id !== action.messageID)
      }
    default:
      return state
  }
}

export const initialState = {
  messages: []
}
import React from 'react'
import { reducer, initialState } from './reducer'

export const SnackbarContext = React.createContext({
  state: initialState,
  dispatch: () => null
})

export const SnackbarProvider = ({ children }) => {
  const [state, dispatch] = React.useReducer(reducer, initialState)

  return (
    <SnackbarContext.Provider value={[ state, dispatch ]}>
    	{ children }
    </SnackbarContext.Provider>
  )
}
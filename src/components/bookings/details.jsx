import { Button, SegmentedControl, DatePicker } from '@bitcine/cinesend-theme'
import { useBookings } from '/src/hooks/bookings'
import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import { useAuth } from '/src/hooks/auth'
import { Link } from 'react-router-dom'
import BookingTitle from './booking_title'
import BookingDescription from './booking_description'
import CreateStatusUpdate from './create_status_update'
import { options } from './options'
import { convertToLocal, convertToLocalDateTime } from '/src/helpers/convert_date'

const deliveryMapping = {
  'e-delivery': 1,
  'hard-drive': 0,
  satellite: -1,
}

const Details = ({ booking }) => {
  const [pending, setPending] = useState(false)
  const [isCompleted, setIsCompleted] = useState(booking?.overall_status === 'completed')
  const { checkPermission } = useAuth()
  const { startDeliveries, updateBooking } = useBookings()

  useEffect(() => {
    setIsCompleted(booking?.overall_status === 'completed')
  }, [booking])
  const editDate = (targetField, initValue) => (
    <DatePicker
      className={'w-full md:w-2/3'}
      showJumpToToday={true}
      minDate={dayjs()}
      isClearable={false}
      disabled={!checkPermission('update-bookings')}
      date={dayjs(initValue).tz('GMT').toString()}
      showTimeSelect={false}
      onChange={(date) => {
        let payload = {}
        payload['bookingID'] = booking.id
        payload[targetField] = dayjs(date).hour(0).minute(0).toString()
        updateBooking(payload)
      }}
    />
  )

  const details = {
    'Overall Status': options[booking.overall_status],
    'Delivery Date': (
      <>{isCompleted ? convertToLocal(booking.deliver_at) : editDate('deliver_at', booking.deliver_at)}</>
    ),
    'Engagement Date': (
      <>{isCompleted ? convertToLocal(booking.release_date) : editDate('release_date', booking.release_date)}</>
    ),
    Title: (
      <Link to={`/titles/${booking.title?.id}/details`}>
        <BookingTitle booking={booking} />
      </Link>
    ),
    Release: (
      <Link to={`/titles/${booking.title?.id}/releases`}>
        <BookingDescription booking={booking}></BookingDescription>
      </Link>
    ),
    Transfers: booking.transfer_counts?.transfers_total ?? 0,
    Circuit: booking.cinema?.circuit,
    Vendor: <Link to={`/organizations/${booking.organization?.id}/details`}>{booking.organization?.name}</Link>,
  }
  return (
    <div className='w-full text-sm flex items-start space-x-4'>
      <div className='w-full'>
        <h5 className='mb-4'>
          <Link to={`/sites/${booking.cinema?.id}/bookings`}>{booking.cinema?.name}</Link>
        </h5>
        <div className='space-y-1'>
          {Object.keys(details).map((key) => (
            <div key={key} className='flex flex-col md:flex-row items-center space-x-2 h-10'>
              <div className='w-2/5 text-right pr-2'>{key}:</div>
              <div className='w-3/5 font-medium'>{details[key]}</div>
            </div>
          ))}
          <div className='flex pt-4 items-center space-x-1'>
            <div className='w-2/5 pr-2 text-sm text-right'>Method:</div>
            <SegmentedControl
              className={'w-full md:w-1/2'}
              size='small'
              disabled={!checkPermission('update-bookings')}
              options={[
                {
                  label: 'E-Delivery',
                  value: 'e-delivery',
                },
                {
                  label: 'Physical Delivery',
                  value: 'hard-drive',
                },
                {
                  label: 'Satellite',
                  value: 'satellite',
                  hide: !booking.cinema.satellite_capable,
                },
              ].filter((opt) => !opt.hide)}
              value={
                booking.is_electronic == 1 ? 'e-delivery' : booking.is_electronic == 0 ? 'hard-drive' : 'satellite'
              }
              onChange={(data) => {
                const is_electronic = deliveryMapping[data]
                updateBooking({ bookingID: booking.id, is_electronic })
              }}
            />
          </div>
        </div>
      </div>
      <div className='w-full space-y-2 border-l border-black/10 px-4'>
        <h5 className='mb-4'>Status Updates</h5>
        {booking.statuses.length === 0 && <div className='flex justify-center w-full'>No status updates!</div>}
        {booking.statuses.map((status, index) => (
          <div key={index} className='flex items-start space-x-4'>
            <div className='flex flex-col'>
              <div className='font-medium'>{options[status.status]}</div>
              <div className='text-gray-500 text-xs'>{convertToLocalDateTime(status.created_at)}</div>
            </div>
          </div>
        ))}
        <div className='flex space-x-4'>
          <CreateStatusUpdate booking={booking} />
          {booking.is_electronic &&
            ['pending', 'accepted'].includes(booking.latest_status.toLowerCase()) &&
            booking.transfer_counts?.transfers_total > 0 && (
              <Button
                size='small'
                type={'success'}
                disabled={!checkPermission('update-bookings') || pending}
                className='w-full md:w-1/2 lg:w-1/3'
                icon='download'
                onClick={() => {
                  setPending(true)
                  startDeliveries({ booking, onComplete: () => setPending(false) })
                }}
              >
                Start{' '}
                {booking.transfer_counts?.transfers_total === 1
                  ? 'transfer'
                  : `${booking.transfer_counts?.transfers_total} transfers`}
              </Button>
            )}
        </div>
      </div>
    </div>
  )
}

export default Details

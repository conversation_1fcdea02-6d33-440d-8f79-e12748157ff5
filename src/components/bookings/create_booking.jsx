import { Button } from '@bitcine/cinesend-theme'
import { useState } from 'react'
import { useAuth } from '/src/hooks/auth'
import BookingModal from './booking_modal'

const CreateBooking = () => {
  const { checkPermission } = useAuth()

  const [bookingModalOpen, setBookingModalOpen] = useState(false)

  return (
    <>
      {checkPermission('create-bookings') &&
      <Button icon='add' onClick={() => setBookingModalOpen(true)}>
        Create booking
      </Button>}
      {bookingModalOpen &&
        <BookingModal onClose={() => setBookingModalOpen(false)}/>}
    </>
  )
}

export default CreateBooking

import { useState } from 'react'
import ReactJson from 'react-json-view'
import { Table } from '@bitcine/cinesend-theme'
import { convertToLocalDateTime } from '/src/helpers/convert_date'
import Slide from '/src/components/slide'

function Transactions ({ transactions }) {
  const [showTransaction, setShowTransaction] = useState(null)
  return (
    <>
      <Table
        header={{
          columns: [
            { text: 'Type' },
            { text: 'Description' },
            { text: 'Compositions' },
            { text: 'Created Date', key: 'created_at' }
          ]
        }}
        widths={[100, 'auto', 120, 240]}
        body={{
          data: transactions,
          row: {
            spaced: true,
            onClick: (event, data) => setShowTransaction(data),
            render: [
              ({ transaction_data}) => transaction_data?.transactionType,
              ({ transaction_data}) => <>{transaction_data?.release?.titleName}<br/>
                {transaction_data?.release?.description}</>,
              ({ transaction_data}) => transaction_data?.release?.compositions.length,
              ({ created_at }) => convertToLocalDateTime(created_at)
            ]
          },
          empty: {
            title: 'No transactions found!',
            text: 'This booking has no transactions.',
            icon: 'cloud_sync'
          }
        }}
      />
      {showTransaction && <Slide
        header='Full Transaction Data'
        onClose={() => setShowTransaction(null)}>
        <ReactJson
          src={showTransaction.transaction_data}
          name={null}
          collapsed={1}
          enableClipboard={false}
          displayObjectSize={false}
          displayDataTypes={false}/>
      </Slide>}
    </>
  )
}

export default Transactions

import dayjs from 'dayjs'
import { DatePicker, Modal, Select, Toggle } from '@bitcine/cinesend-theme'
import { useState } from 'react'
import { useBookings } from '/src/hooks/bookings'
import ErrorMessages from '/src/components/error_messages'
import { useQuery } from '@tanstack/react-query'

const BookingModal = ({ onClose }) => {

  const [errors, setErrors] = useState([])
  const [titleID, setTitleID] = useState(null)
  const [releaseID, setReleaseID] = useState(null)
  const [siteID, setSiteID] = useState(null)
  const [engagementDate, setEngagementDate] = useState(null)
  const [deliveryDate, setDeliveryDate] = useState(null)
  const [isElectronic, setElectronicDelivery] = useState(true)
  const [pending, setPending] = useState(false)

  const sitesUrl = '/api/cinemas?take=1000'
  const { data: sites } = useQuery({ queryKey: [sitesUrl]})
  const siteOptions = sites ? sites.data.map(site => ({ label: site.name, value: site.id })) : []

  const titlesUrl = '/api/titles?take=500'
  const { data: titles } = useQuery({ queryKey: [titlesUrl] }) 
  const titleOptions = titles ? titles.data.map(title => ({ label: title.friendly_title, value: title.id })) : []

  const releasesUrl = `/api/titles/${titleID}/releases`
  const { data: releases } = useQuery({ queryKey: [releasesUrl] })
  const releaseOptions = releases
    ? releases.data.map(pkg => ({ label: pkg.package_name, value: pkg.id }))
    : []

  const { createBooking } = useBookings()
  const create = () => {
    setPending(true)
    createBooking({
      package_id: releaseID,
      cinema_site_id: siteID,
      release_date: engagementDate,
      deliver_at: deliveryDate,
      is_electronic: isElectronic,
      setErrors: errors => {
        setPending(false)
        setErrors(errors)
      },
      onComplete: () => {
        onClose()
      }
    })
  }

  return (
    <Modal
      header='Create new booking'
      onClose={onClose}
      confirmButton={{
        text: 'Create booking',
        disabled: pending || !releaseID || !siteID || !titleID,
        onClick: () => create()
      }}>
      <div className='flex flex-col space-y-4'>
        <Select
          label='Title'
          options={titleOptions}
          value={titleOptions.find(opt => opt.value === titleID)}
          onChange={opt => setTitleID(opt.value)}/>
        <Select
          label='Release'
          disabled={!titleID}
          options={releaseOptions}
          value={releaseOptions.find(opt => opt.value === releaseID)}
          onChange={opt => setReleaseID(opt.value)}/>
        <Select
          label='Site'
          options={siteOptions}
          value={siteOptions.find(opt => opt.value === siteID)}
          onChange={opt => setSiteID(opt.value)}/>
        <DatePicker
          label='Delivery Date'
          showJumpToToday={true}
          minDate={dayjs()}
          date={deliveryDate}
          showTimeSelect={false}
          onChange={date => setDeliveryDate(date)}/>
        <DatePicker
          label='Engagement Date'
          showJumpToToday={true}
          minDate={dayjs()}
          date={engagementDate}
          showTimeSelect={false}
          onChange={date => setEngagementDate(date)}/>
        <Toggle
          size='small'
          label='Electronic Delivery'
          checked={isElectronic}
          onChange={() => setElectronicDelivery(!isElectronic)}/>
        <ErrorMessages errors={errors}/>
      </div>
    </Modal>
  )
}

export default BookingModal

import React from 'react'
import { Status } from '@bitcine/cinesend-theme'
import { useQuery } from '@tanstack/react-query'
import DeviceComponent from './device'

const Device = ({ site }) => {

  const apiUrl = `/api/media-managers/${site.primary_media_manager?.id}`
  const { data, isFetching, isLoading } = useQuery({ queryKey: [apiUrl], refetchInterval: 30000 })

  const device = data?.mediaManager

  return (
    <div className="h-full">
      <Status pending={isLoading} pendingMessage={'Establishing connection to device...'}>
        {device
          ? <DeviceComponent device={device} site={site} fetching={isFetching}/>
          : null}
      </Status>
    </div>
  )
}

export default Device

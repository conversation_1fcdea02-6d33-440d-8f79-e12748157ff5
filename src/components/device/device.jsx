import React, { useState } from 'react'
import { Device, Icon } from '@bitcine/cinesend-theme'
import { useJobs } from '/src/hooks/jobs'
import SmallLogo from '/src/components/logos/small_logo'
import { convertToFromNow } from '/src/helpers/convert_date'
import { useSnackbar } from '/src/hooks/snackbar'

const DeviceComponent = ({ site, device, fetching = false }) => {

  const { addMessage } = useSnackbar()
  const { createJob } = useJobs()
  const [selectedDriveCPLs, setSelectedDriveCPLs] = useState([])
  const [selectedLibraryCPLs, setSelectedLibraryCPLs] = useState([])

  const setErrors = (errors) => {
    errors.forEach(error => {
      addMessage(error, 'error')
    })
  }
  const onComplete = () => {
    setSelectedDriveCPLs([])
    setSelectedLibraryCPLs([])
  }

  const onCopyFromLibraryToDrive = (cpls, drive) => {
    createJob({
      setErrors, onComplete, cpls, drive, site_id: site.id,
      code: 'ingest_cpl_from_library_to_drive'
    })
  }
  const onDeleteFromLibrary = (cpls) => {
    createJob({
      setErrors, onComplete, cpls, site_id: site.id,
      type: 'library',
      code: 'delete_cpl'
    })
  }

  const onFormatDrive = (drive) => {
    if (!window.confirm(`Are you sure you want to format ${drive.location_name}`)) {
      return
    }
    createJob({
      setErrors, onComplete, drive, site_id: site.id,
      code: 'format_drive'
    })
  }

  const onValidateOnDrive = (cpls, drive) => {
    createJob({
      setErrors, onComplete, cpls, drive, site_id: site.id,
      code: 'validate_dcp'
    })
  }
  const onDeleteFromDrive = (cpls, drive) => {
    createJob({
      setErrors, onComplete, cpls, drive, site_id: site.id,
      type: 'drive',
      code: 'delete_dcp'
    })
  }
  const onCopyFromDriveToLibrary = (cpls, drive) => {
    createJob({
      setErrors, onComplete, cpls, drive, site_id: site.id,
      code: 'ingest_cpl_from_drive_to_library'
    })
  }
  const onCopyFromDriveToDrive = (cpls, sourceDrive, destinationDrive) => {
    createJob({
      setErrors, onComplete, cpls, sourceDrive, destinationDrive, site_id: site.id,
      code: 'ingest_cpl_from_drive_to_drive'
    })
  }
  const onReboot = () => {
    createJob({
      setErrors, onComplete, site_id: site.id,
      type: 'reboot',
      code: 'reboot',
      name: 'Reboot Device'
    })
  }

  return (
    <Device
      headerOn={false}
      printLabel={false}
      topOffsetPx={190}
      allowedSettings={false}
      logo={<SmallLogo/>}
      header={<div className='flex items-center space-x-3'>
        <div className='text-lg font-semibold'>{device.name}</div>
        <Icon
          icon={'circle'}
          className={`text-2xs
            ${!device.is_online ? 'text-gray-600' : 'text-success-600'}
            ${fetching ? 'animate-bounce' : 'text-2xs'}
          `}
        />
        {!device.is_online && <div className="text-xs text-gray-600 italic">
          (Last online: {convertToFromNow(device.status_updated_at)})
        </div>}
      </div>}
      selectedDriveCPLs={selectedDriveCPLs}
      setSelectedDriveCPLs={setSelectedDriveCPLs}
      selectedLibraryCPLs={selectedLibraryCPLs}
      setSelectedLibraryCPLs={setSelectedLibraryCPLs}
      onCopyFromLibraryToDrive={onCopyFromLibraryToDrive}
      onDeleteFromLibrary={onDeleteFromLibrary}
      onFormatDrive={onFormatDrive}
      onValidateOnDrive={onValidateOnDrive}
      onDeleteFromDrive={onDeleteFromDrive}
      onCopyFromDriveToLibrary={onCopyFromDriveToLibrary}
      onCopyFromDriveToDrive={onCopyFromDriveToDrive}
      onReboot={onReboot}
      settings={device.settings}
      library={{
        cpls: device.media_library.cpls ?? [],
        disk_usage: device.media_library.disk_usage ?? {}
      }}
      drives={device.drives}
      downloads={device.downloads}/>
  )
}

export default DeviceComponent

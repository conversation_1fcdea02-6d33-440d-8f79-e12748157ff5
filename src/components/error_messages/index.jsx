const ErrorMessages = ({ errors = [] }) => {
  if (errors.length === 0) {
    return null
  }
  return (
    <div className='bg-error-500 text-white p-4 font-semibold text-sm rounded-md space-y-4'>
      <div>{errors.length === 1 ? 'An error occurred:' : 'The following errors occurred:'}</div>
      <div>{errors.map(error => <div key={error} className='text-xs'>{error}</div>)}</div>
    </div>
  )
}

export default ErrorMessages
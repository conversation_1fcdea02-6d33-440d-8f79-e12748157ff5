import React from 'react'
import { DeliveryContext } from '/src/contexts/delivery'
import { useDeliveries } from '/src/hooks/deliveries'
import CreateStatusUpdate from './create_status_update'
import humanFileSize from '/src/helpers/human_file_size'
import Slide from '/src/components/slide'
import TriggerDownload from './trigger_download'
import { useAuth } from '/src/hooks/auth'

const Details = () => {
  const [state] = React.useContext(DeliveryContext)
  const { showDelivery } = useDeliveries()
  const { checkPermission } = useAuth()
  const delivery = state.delivery
  if (!delivery) {
    return null
  }
  const details = {
    'Status': delivery.status,
    'Version': delivery.version?.nickname
      ? delivery.version?.nickname
      : delivery.version?.version_name,
    'Size': humanFileSize(delivery.version?.size),
    'Circuit': delivery.cinema?.circuit,
    'Vendor': delivery.organization?.name,
    'Method': delivery.is_electronic ? 'E-Delivery' : 'Hard drive'
  }
  return (
    <Slide header='Transfer Details' onClose={() => showDelivery(null)}>
      <div>
        <h4 className="mt-2 mb-4">{delivery.cinema?.name}</h4>
        <div className="space-y-1">
          {Object.keys(details).map(key =>
            <div key={key} className="flex items-center space-x-1">
              <div>{key}:</div>
              <div className="font-medium capitalize">{details[key]}</div>
            </div>)}
        </div>
      </div>
      {/* <div className="mt-4 space-y-4 relative border-y border-black/10 py-4">
        {delivery.statuses.length === 0
          ? <div className="flex justify-center w-full">No status updates!</div>
          : <div className="absolute top-8 left-3 bottom-8 border-l-2 border-gray-300"/>}
        {delivery.statuses.map((status, index) =>
          <div key={index} className="flex items-start space-x-4">
            <div className="bg-gray-300 rounded-full w-6 h-6"/>
            <div className="flex flex-col">
              <div className="font-semibold">{options[status.status]}</div>
              <div className="text-gray-500">{convertToLocalDateTime(status.created_at)}</div>
            </div>
          </div>
        )}
      </div> */}
      {checkPermission('update-deliveries') &&
        <div className="flex space-x-4 mt-4">
          <CreateStatusUpdate delivery={delivery}/>
          <TriggerDownload delivery={delivery}/>
        </div>
      }
    </Slide>
  )

}

export default Details

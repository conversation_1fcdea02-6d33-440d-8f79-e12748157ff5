import { Button, Select, Modal } from '@bitcine/cinesend-theme'
import { useState } from 'react'
import { options } from './options'
import { useDeliveries } from '/src/hooks/deliveries'
import ErrorMessages from '/src/components/error_messages'

const CreateStatusUpdate = ({ delivery }) => {
  const { updateDelivery } = useDeliveries()
  const [code, setCode] = useState()
  const [pending, setPending] = useState(false)
  const [open, setOpen] = useState(false)
  const [errors, setErrors] = useState([])
  const update = () => {
    setPending(true)
    updateDelivery({
      deliveryID: delivery.id,
      status: code,
      onComplete: () => {
        setPending(false)
        setOpen(false)
      },
      setErrors: errors => {
        setPending(false)
        setErrors(errors)
      }
    })
  }
  const selectOptions = Object.keys(options).map(key => ({ value: key, label: options[key] }))
  return (
    <>
      <Button className='w-full' icon='add' onClick={() => setOpen(true)}>Add status update</Button>
      {open && <Modal
        header='New Status Update'
        onClose={() => setOpen(false)}
        confirmButton={{
          text: 'Add status update',
          disabled: !code || pending,
          onClick: () => update()
        }}>
        <Select
          label='Select an update'
          options={selectOptions}
          value={selectOptions.find(opt => opt.value === code)}
          onChange={opt => setCode(opt.value)}/>
        <ErrorMessages errors={errors}/>
      </Modal>}
    </>
  )
}

export default CreateStatusUpdate

import { Button } from '@bitcine/cinesend-theme'
import { useDeliveries } from '/src/hooks/deliveries'
import { useState } from 'react'

const TriggerDownload = ({ delivery }) => {
  
  const [pending, setPending] = useState(false)

  const { sendDelivery } = useDeliveries()

  if (!delivery.is_electronic || !delivery.status) {
    return null
  }
  if (delivery.status.toLowerCase() !== 'pending') {
    return null
  }
  return (
    <Button
      type={'success'}
      className='w-full'
      icon="download"
      disabled={pending}
      onClick={() =>  {
        setPending(true)
        sendDelivery(delivery, () => setPending(false))
      }}>
      Start eDelivery
    </Button>
  )
}

export default TriggerDownload

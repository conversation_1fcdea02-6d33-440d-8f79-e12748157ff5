import React from 'react'
import { Input, Select } from '@bitcine/cinesend-theme'
import Card from '/src/components/layouts/card'

const FieldsForm = ({ title, data, layout = '',
  fields, updateState, saveField, disabled }) =>
  <Card title={title} layout={layout}>
    {
      fields.map(({ label, key, type, options = [],
        render = null, width = 'col-span-3' }, index) => ({
        'input':
          <Input
            width={width}
            key={index}
            disabled={disabled}
            label={label}
            value={typeof render === 'function' ? render(data[key]) : data[key]}
            onChange={e => updateState({ [key]: e.target.value })}
            onBlur={() => saveField({ [key]: data[key] })}/>,
        'select':
          <Select
            width={width}
            key={index}
            disabled={disabled}
            label={label}
            value={options.filter(opt => opt.value === data[key])}
            options={options}
            onChange={option => {
              updateState({ [key]: option.value })
              saveField({ [key]: option.value })
            }}/>
      }[type] ?? null))
    }
  </Card>

export default FieldsForm

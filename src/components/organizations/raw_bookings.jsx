import { useState } from 'react'
import { Table, Button } from '@bitcine/cinesend-theme'
import { convertToLocalDateTime } from '/src/helpers/convert_date'
import { useSnackbar } from '/src/hooks/snackbar'
import RawBookingModal from './raw_booking_modal'
import { useQuery } from '@tanstack/react-query'

function RawBookingsTable ({ organization }) {

  let { addMessage } = useSnackbar()

  const [open, setOpen] = useState(false)
  const [active, setActive] = useState({})

  const apiUrl = `/api/organizations/${organization.id}/transactions`
  const defaults = localStorage.getItem(apiUrl) !== null
    ? JSON.parse(localStorage.getItem(apiUrl))
    : { search: '', page: 0, take: 15, sortBy: 'created_at', direction: 'DESC' }

  const [queries, setQueries] = useState(defaults)

  const params = new URLSearchParams(queries).toString()
  const url = `${apiUrl}?${params}`

  const setQuery = (data) => {
    const newQueries = { ...queries, ...data }
    // if a page wasn't clicked, and anything else was changed reset page to 0.
    if (data && !data.page) {
      newQueries.page = 0
    }
    setQueries(newQueries)
    localStorage.setItem(apiUrl, JSON.stringify(newQueries))
  }

  const { data: transactions, error } = useQuery({ queryKey: [url], refetchOnWindowFocus: true }) 

  return (
    <div className="flex flex-col space-y-4">
      <Table
        status={{
          pending: !transactions,
          pendingMessage: 'Loading transaction records...',
          error: error
        }}
        header={{
          columns: [
            { text: 'Title ' },
            { text: 'Submitted By' },
            { text: 'Status' },
            { text: 'Date Received', key: 'created_at' },
            { text: '' }
          ],
          sorting: {
            key: queries.sortBy,
            sortingKey: queries.sortBy,
            direction: queries.sortDirection,
            onSortChange: (sort) => setQuery({ sortBy: sort.key, sortDirection: sort.direction })
          }
        }}
        widths={[200, 90, 90, 110, 60]}
        body={{
          data: transactions?.data,
          row: {
            spaced: true,
            onClick: (event, data) => {
              setOpen(true)
              setActive(data.transaction_data)
            },
            render: [
              ({ id,  transaction_data }) => <div className="flex flex-col">
                <span>id: {id} : {transaction_data?.release?.titleName ??
                  transaction_data?.filename ??
                  'Information unavailable for this transaction type.'}
                </span>
                <span className="text-xs font-light">{transaction_data?.release?.description}</span>
              </div>,
              ({ user }) => user?.name,
              ({ processed }) => (processed ? 'Processed' : 'Unprocessed'),
              ({ created_at }) => convertToLocalDateTime(created_at),
              ({ processed, id }) => ''
            ]
          },
          empty: {
            text: 'No transaction records found for this studio.',
            icon: 'domain'
          }
        }}
        paginate={{
          totalRows: transactions?.total,
          currentPage: transactions?.current_page - 1,
          rowsPerPage: transactions?.per_page,
          onPageChange: page => setQuery({ page }),
          onRowsPerPageChange: take => setQuery({ take })
        }}
      />
      {open &&
        <RawBookingModal data={active} onClose={() => setOpen(false)}/>
      }
    </div>
  )
}

export default RawBookingsTable

import { Select } from '@bitcine/cinesend-theme'

function SelectType({ initValue, onChange, locked }) {
  const isAdminType = initValue === 'admin'
  let options = [
    { label: 'Studio', key: 'studio' },
    { label: 'Vendor', key: 'vendor' },
    { label: 'Site', key: 'exhibitor' }, // for now
  ]

  if (isAdminType) {
    options = [...options, { label: 'Admin', key: 'admin' }]
  }

  return (
    <Select
      disabled={isAdminType || locked}
      label='Type'
      options={options}
      value={options.filter((opt) => opt.key === initValue)}
      onChange={(option) => onChange(option.key)}
    />
  )
}

export default SelectType

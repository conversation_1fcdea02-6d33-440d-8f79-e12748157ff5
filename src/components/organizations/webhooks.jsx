import dayjs from 'dayjs'
import React, { useState } from 'react'
import ErrorMessages from '/src/components/error_messages'
import { Table, Select, Input, Button, ButtonDropdown } from '@bitcine/cinesend-theme'
import { convertToLocalDateTime } from '/src/helpers/convert_date'
import { useOrganizations } from '/src/hooks/organizations'
import { useQueryClient, useQuery } from '@tanstack/react-query'

const Webhooks = ({ organization }) => {
  const { updateWebhook, createWebhook, deleteWebhook } = useOrganizations()

  const [errors, setErrors] = useState([])
  const [id, setId] = useState(null)
  const [url, setUrl] = useState('')
  const [token, setToken] = useState('')
  const [eventType, setEventType] = useState('')
  const [eventModel, setEventModel] = useState('')
  const queryClient = useQueryClient()

  const [pending, setPending] = useState(false)

  const TOKENS_URL = '/api/organizations/{organizationId}/webhooks'
  const dataUrl = TOKENS_URL.replace('{organizationId}', organization.id)
  const { data, isLoading, error } = useQuery({ queryKey: [dataUrl] })
  const mutate = () => queryClient.invalidateQueries({ queryKey: [dataUrl] })

  const editUrl = (url) => {
    setId(url.id ?? null)
    setUrl(url.url)
    setToken(url.bearer_token)
    setEventType(url.event_type)
    setEventModel(url.event_model)
  }

  const clearEdit = () => {
    setId(null)
    setUrl('')
    setToken('')
    setEventType('')
    setEventModel('')
  }

  const save = () => {
    if (id) {
      updateWebhook({
        setErrors,
        organizationID: organization.id,
        id,
        onComplete: () => {
          clearEdit()
          mutate()
        },
        event_type: eventType,
        event_model: eventModel,
        url,
        bearer_token: token
      })
    }
    else {
      createWebhook({
        setErrors, organizationID: organization.id, onComplete: () => {
          clearEdit()
          mutate()
        },
        event_type: eventType, event_model: eventModel, url, bearer_token: token
      })
    }
  }

  const typeOptions = [{ value: 'update', label: 'Update' }]
  const modelOptions = [{ value: 'booking', label: 'Booking' }]

  return (
    <div className="flex flex-col space-y-4 max-w-3xl">
      <>
        <Select label="On" options={typeOptions}
          onChange={(value) => setEventType(value.value)}
          value={typeOptions.find(opt => opt.value === eventType)}
        />
        <Select label="Of" options={modelOptions}
          onChange={(value) => setEventModel(value.value)}
          value={modelOptions.find(opt => opt.value === eventModel)}
        />
        <Input label="Send response to (URL):"
          value={url}
          onChange={(e) => setUrl(e.target.value)}
          onBlur={(e) => setUrl(e.target.value)}
        />
        <Input label="With `Authorization: Bearer` token (optional)"
          value={token}
          onChange={(e) => setToken(e.target.value)}
          onBlur={(e) => setToken(e.target.value)}
        />
        <div className="flex space-x-4">
          <Button className="w-1/4" secondary={true} onClick={() => clearEdit()}>Clear</Button>
          <Button className="w-1/4" disabled={(!url || !eventModel || !eventType)}
            onClick={() => save()}>{id ? 'Save' : 'Create'}</Button>
        </div>
      </>
      <Table
        status={{
          pending: isLoading,
          pendingMessage: 'Loading Webhooks...',
          error: error
        }}
        header={{
          columns: [
            { text: 'Webhook URL' },
            { text: 'Used for' },
            { text: 'Created Date' },
            { text: 'Edit' }
          ]
        }}
        widths={['auto', 200, 300, 120]}
        body={{
          data: data?.data.data ?? [],
          row: {
            spaced: true,
            render: [
              ({ url }) => url,
              ({ event_model, event_type }) => <div>on {event_type} of {event_model}</div>,
              ({ created_at }) => convertToLocalDateTime(created_at),
              (url) => <ButtonDropdown
                button={{ minimal: true, icon: 'more_vert', type: 'neutral' }}
                dropdown={{
                  content: [
                    {
                      text: 'Edit',
                      onClick: () => {
                        editUrl(url)
                      }
                    },
                    {
                      text: 'Delete',
                      onClick: () => {
                        if (window.confirm('Are you sure you want to delete this webhook?')) {
                          deleteWebhook({setErrors, organizationID:
                            organization.id, id: url.id, onComplete: () => { mutate() } })
                        }
                      }
                    }
                  ]
                }}/>
            ]
          },
          empty: {
            title: 'No Webhooks found!',
            text: '',
            icon: 'link'
          }
        }}
      />

      <ErrorMessages errors={errors}/>
    </div>
  )

}

export default Webhooks

import { Select } from '@bitcine/cinesend-theme'
import { useQuery } from '@tanstack/react-query'

function SelectCinemaSite ({ value, onChange, className, disabled = false, label = 'Site' }) {

  const pageUrl = '/api/cinemas?take=200'
  const { data } = useQuery({ queryKey: [pageUrl] })

  const sites = data?.data.map(site => ({ value: site.id, label: site.name }))

  return (
    <Select
      disabled={disabled}
      className={className}
      label={label}
      loading={!sites}
      options={sites}
      value={sites && sites.find(site => site.value === value)}
      onChange={site => onChange(site.value)}
    />
  )
}

export default SelectCinemaSite

import { Button, Modal, Input } from '@bitcine/cinesend-theme'
import React, { useState } from 'react'
import { useOrganizations } from '/src/hooks/organizations'
import ErrorMessages from '/src/components/error_messages'
import { useAuth } from '/src/hooks/auth'
import SelectType from './select_type'

const CreateOrganization = () => {
  const [errors, setErrors] = useState([])
  const [open, setOpen] = useState(false)
  const [data, setData] = useState({})
  const update = (newData) => setData({ ...data, ...newData })
  const [pending, setPending] = useState(false)
  const { createOrganization } = useOrganizations()
  const { checkPermission } = useAuth()
  const submit = () => {
    setPending(true)
    createOrganization({
      ...data,
      setErrors: (errors) => {
        setPending(false)
        setErrors(errors)
      },
    })
  }
  return (
    <>
      {checkPermission('create-organizations') && (
        <Button icon='add' onClick={() => setOpen(true)}>
          Create organization
        </Button>
      )}
      {open ? (
        <Modal
          header='Create new organization'
          onClose={() => setOpen(false)}
          pending={pending}
          confirmButton={{
            text: 'Create organization',
            onClick: () => submit(),
            disabled: !data.name || !data.type,
          }}
        >
          <div className='flex flex-col space-y-4'>
            <Input
              placeholder='Site name, studio name, vendor name, etc.'
              label='Organization Name'
              value={data.name}
              onChange={(e) => update({ name: e.target.value })}
            />
            <SelectType
              initValue={data.type}
              onChange={(type) => {
                update({ type })
              }}
            />
            <ErrorMessages errors={errors} />
          </div>
        </Modal>
      ) : null}
    </>
  )
}

export default CreateOrganization

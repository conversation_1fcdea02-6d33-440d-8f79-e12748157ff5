import { useState } from 'react'
import { Table } from '@bitcine/cinesend-theme'
import { convertToLocal } from '/src/helpers/convert_date'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '/src/hooks/auth'
import { useQuery } from '@tanstack/react-query'

const ORGANIZATIONS_API = '/api/organizations'

function OrganizationsTable() {
  const { checkPermission } = useAuth()

  const defaults =
    localStorage.getItem(ORGANIZATIONS_API) !== null
      ? JSON.parse(localStorage.getItem(ORGANIZATIONS_API))
      : { search: '', page: 0, take: 15 }

  const [queries, setQueries] = useState(defaults)

  const params = new URLSearchParams(queries).toString()
  const url = `${ORGANIZATIONS_API}?${params}`

  const setQuery = (data) => {
    const newQueries = { ...queries, ...data }
    // if a page wasn't clicked, and anything else was changed reset page to 0.
    if (data && !data.page) {
      newQueries.page = 0
    }
    setQueries(newQueries)
    localStorage.setItem(ORGANIZATIONS_API, JSON.stringify(newQueries))
  }

  const { data: organizations, error } = useQuery({ queryKey: [url] })

  // temp: orgs will have 'exhibitor' types map to 'site'
  const typeMap = {
    exhibitor: 'Site',
    site: 'Site',
    vendor: 'Vendor',
    studio: 'Studio',
    admin: 'Admin',
  }

  const navigate = useNavigate()

  return (
    <div className='flex flex-col space-y-4'>
      <Table
        status={{
          pending: !organizations,
          pendingMessage: 'Loading organizations...',
          error: error,
        }}
        header={{
          columns: [{ text: 'Organization' }, { text: 'Type' }, { text: 'Created' }],
        }}
        widths={['auto', 300, 300]}
        body={{
          data: organizations?.data,
          row: {
            spaced: true,
            onClick: checkPermission('view-organizations')
              ? (event, data) => navigate(`/organizations/${data.id}`)
              : null,
            render: [
              ({ name }) => <div className='font-medium'>{name}</div>,
              ({ type }) => <div className='capitalize'>{typeMap[type]}</div>,
              ({ created_at }) => convertToLocal(created_at),
            ],
          },
          empty: {
            organization: 'No organizations found!',
            text: 'Refine your filters.',
            icon: 'domain',
          },
        }}
        paginate={{
          totalRows: organizations?.total,
          currentPage: organizations?.current_page - 1,
          rowsPerPage: organizations?.per_page,
          onPageChange: (page) => setQuery({ page }),
          onRowsPerPageChange: (take) => setQuery({ take }),
        }}
      />
    </div>
  )
}

export default OrganizationsTable

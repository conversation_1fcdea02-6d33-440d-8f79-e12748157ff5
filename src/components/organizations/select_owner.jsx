import { Select } from '@bitcine/cinesend-theme'
import { useQuery } from '@tanstack/react-query'

function SelectOwner ({ organizationID, value, onChange, locked }) {

  const pageUrl = `/api/organizations/${organizationID}/users`
  const { data } = useQuery({ queryKey: [pageUrl] })

  const users = data?.data.map(user => ({ value: user.id, label: user.name }))

  return (
    <Select
      label='Owner'
      loading={!users}
      disabled={locked}
      options={users}
      value={users && users.find(org => org.value === value)}
      onChange={option => onChange(option.value)}
    />
  )
}

export default SelectOwner

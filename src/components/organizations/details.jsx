import React, { useState } from 'react'
import { Button, Input } from '@bitcine/cinesend-theme'
import { useOrganizations } from '/src/hooks/organizations'
import ErrorMessages from '/src/components/error_messages'
import getUpdatedData from '/src/helpers/get_updated_data'
import SelectOwner from './select_owner'
import SelectType from './select_type'
import { useAuth } from '/src/hooks/auth'

const Details = ({ organization }) => {
  const [errors, setErrors] = useState([])
  const [data, setData] = useState(organization)
  const [pending, setPending] = useState(false)
  const { checkPermission } = useAuth()
  const canUpdate = checkPermission('update-organizations')

  const update = (newData) => {
    setData({ ...data, ...newData, setErrors })
  }
  const { updateOrganization } = useOrganizations()
  const save = () => {
    setPending(true)
    updateOrganization({
      organizationID: organization.id,
      ...getUpdatedData(organization, data),
      onComplete: () => setPending(false),
      setErrors: (errors) => {
        setPending(false)
        setErrors(errors)
      },
    })
  }
  return (
    <div className='flex flex-col space-y-4 max-w-2xl'>
      <Input
        label='Organization Name'
        value={data.name}
        disabled={!canUpdate}
        onChange={(e) => update({ name: e.target.value })}
      />
      <SelectOwner
        value={data.owner_id}
        organizationID={organization.id}
        locked={!canUpdate}
        onChange={(ownerID) => update({ owner_id: ownerID })}
      />
      <SelectType initValue={data.type} locked={!canUpdate} onChange={(type) => update({ type })} />
      <div className='flex justify-end'>
        {canUpdate && (
          <Button onClick={save} disabled={JSON.stringify(data) === JSON.stringify(organization) || pending}>
            Save
          </Button>
        )}
      </div>
      <ErrorMessages errors={errors} />
    </div>
  )
}

export default Details

import { Select } from '@bitcine/cinesend-theme'
import { useQuery } from '@tanstack/react-query'

function SelectOrganization ({ value, onChange, label = 'Organization', disabled = false}) {

  const pageUrl = '/api/organizations?take=100'
  const { data } = useQuery({ queryKey: [pageUrl] })

  const organizations = data?.data.map(org => ({ value: org.id, label: org.name }))

  return (
    <Select
      disabled={disabled}
      label={label}
      loading={!organizations}
      options={organizations}
      value={organizations && organizations.find(org => org.value === value)}
      onChange={organization => onChange(organization.value)}
    />
  )
}

export default SelectOrganization

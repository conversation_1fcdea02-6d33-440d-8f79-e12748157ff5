import { Select } from '@bitcine/cinesend-theme'
import { useQuery } from '@tanstack/react-query'

function SelectWarehouse ({ value, onChange, label = 'Site' }) {

  const pageUrl = '/api/warehouses'
  const { data } = useQuery({ queryKey: [pageUrl] })

  const sites = data?.data.map(site => ({ value: site.id, label: site.name }))

  return (
    <Select
      label={label}
      loading={!sites}
      options={sites}
      value={sites && sites.find(site => site.value === value)}
      onChange={site => onChange(site.value)}
    />
  )
}

export default SelectWarehouse

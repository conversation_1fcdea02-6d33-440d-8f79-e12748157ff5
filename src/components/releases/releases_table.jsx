import { Table } from '@bitcine/cinesend-theme'
import React, { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import ResetTableFilters from '/src/components/reset_table_filters'
import DistributionSummary from './distribution_summary'
import BookingCounts from './booking_counts'
import { convertToLocal } from '/src/helpers/convert_date'
import OrganizationLink from '/src/components/organizations/link'
import TitleLink from '/src/components/titles/link'
import ReleaseLink from './link'
import TogglePin from './toggle_pin'
import ContentVersions from './content_versions'
import ReleaseSatWorkflow from './release_sat_workflow'
import ReleaseTerrestrialWorkflow from './release_terrestrial_workflow'

const RELEASES_API = '/api/releases'

const ReleasesTable = ({ pinned }) => {
  const [filter, setFilter] = useState({})

  const headerDefaults = { search: '', page: 0, take: 15, sortBy: 'created_at', sortDirection: 'DESC', pinned: pinned }

  const urlKey = RELEASES_API

  const defaults = localStorage.getItem(urlKey) !== null ? JSON.parse(localStorage.getItem(urlKey)) : headerDefaults
  const [queries, setQueries] = useState(defaults)
  const queryClient = useQueryClient()

  const params = new URLSearchParams(queries).toString()
  const url = `${urlKey}?${params}`

  const setQuery = (data, isFilter = false, isReset = false) => {
    const newQueries = { ...queries, ...data }
    if ((isFilter && data === null) || isReset) {
      setFilter({})
    }
    // if a page wasn't clicked, and anything else was changed reset page to 0.
    if (data && !data.page) {
      newQueries.page = 0
    }
    setQueries(newQueries)
    localStorage.setItem(RELEASES_API, JSON.stringify(newQueries))
  }

  const { data: releases, error } = useQuery({ queryKey: [url] })
  const mutate = () => queryClient.invalidateQueries({ queryKey: [url] })
  const navigate = useNavigate()

  useEffect(() => {
    setQuery({ pinned: pinned })
  }, [pinned])

  return (
    <>
      <Table
        status={{
          pending: !releases?.data,
          pendingMessage: 'Loading Releases...',
          error: error,
        }}
        header={{
          customElement: (
            <ResetTableFilters
              filterCallback={() => {
                setQuery(headerDefaults, false, true)
              }}
            />
          ),
          columns: [
            { text: 'Studio', key: 'name' },
            { text: 'Title', key: 'friendly_title' },
            { text: 'Name', key: 'package_name' },
            { text: 'Type', key: 'type' },
            { text: 'Total Bookings' },
            { text: 'Booking Summary' },
            { text: 'Distribution Summary' },
            { text: 'Release' },
          ],
          searching: {
            search: queries.search,
            searchPlaceholder: 'Search...',
            onSearch: (search) => setQuery({ search }),
            rightIcon: {
              icon: 'close',
              onClick: () => setQuery({ search: '' }),
              tooltip: {
                text: 'Clear search',
              },
            },
          },
          sorting: {
            options: [
              { key: 'package_name', label: 'Name' },
              { key: 'created_at', label: 'Created Date' },
            ],
            key: queries.sortBy,
            sortingKey: queries.sortBy,
            direction: queries.sortDirection,
            onSortChange: (sort) => setQuery({ sortBy: sort.key, sortDirection: sort.direction }),
          },
        }}
        widths={[200, 200, 200, 200, 160, 160, 160, 160, 'auto']}
        body={{
          data: releases?.data ?? [],
          row: {
            spaced: true,
            onClick: (event, data) => navigate(`/release/${data.id}`),
            render: [
              ({ organization }) => <OrganizationLink organization={organization} />,
              ({ title }) => <TitleLink title={title} />,
              (data) => <ReleaseLink release={data} />,
              ({ types_list, type }) =>
                (types_list.find((element) => element.value === type) ?? { label: 'n/a' }).label,
              ({ booking_counts }) =>
                Object.values(booking_counts ?? []).reduce((partialSum, a) => partialSum + a, 0)
                  ? Object.values(booking_counts ?? []).reduce((partialSum, a) => partialSum + a, 0)
                  : 'None',
              ({ booking_counts }) => <BookingCounts booking_counts={booking_counts} />,
              ({ distribution_summary }) => <DistributionSummary ds={distribution_summary} />,
              ({ title }) => convertToLocal(title?.release_date),
              (data) => <TogglePin release={data} />,
            ],
            expansion: {
              visible: (data, index) => true,
              component: (data, index) => (
                <div className={`border-t py-2 px-4 text-sm flex-col space-y-4`}>
                  <ReleaseSatWorkflow release={data} />
                  <ReleaseTerrestrialWorkflow release={data} />
                  {data.size > 0 && <ContentVersions release={data} />}
                </div>
              ),
            },
          },
          empty: {
            title: 'There are no matches for your filters.',
            text: '',
            icon: 'package',
          },
        }}
        paginate={{
          totalRows: releases?.total,
          currentPage: releases?.current_page - 1,
          rowsPerPage: releases?.per_page,
          onPageChange: (page) => setQuery({ page }),
          onRowsPerPageChange: (take) => setQuery({ take }),
        }}
      />
    </>
  )
}

export default ReleasesTable

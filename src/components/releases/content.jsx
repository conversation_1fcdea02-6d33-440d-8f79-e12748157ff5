import { Table } from '@bitcine/cinesend-theme'
import humanFileSize from '/src/helpers/human_file_size'
import { useNavigate } from 'react-router-dom'
import { useState } from 'react'

function Content({ titleID, content }) {

  const navigate = useNavigate()
  const [search, setSearch] = useState('')
  const [sort, setSort] = useState({
    sortKey: 'version_name',
    sortDirection: 'DESC'
  })

  const data = (() => {
    const filtered = content.filter(item =>
      item.version_name.toLowerCase().includes(search.toLowerCase())
    )
    const sorted = [...filtered].sort((a, b) => {
      const aValue = a[sort.sortKey]
      const bValue = b[sort.sortKey]

      if (sort.sortDirection === 'ASC') {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
      } else {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
      }
    })

    return sorted
  })()

  return (
    <Table
      header={{
        columns: [
          { text: 'Package', key: 'version_name' },
          { text: 'Size', key: 'size' }
        ],
        searching: {
          search: search,
          searchPlaceholder: 'Search...',
          onSearch: search => setSearch(search),
          rightIcon: {
            icon: 'close',
            onClick: () => setSearch({search: ''}),
            tooltip: {
              text: 'Clear search'
            }
          }
        },
        sorting: {
          options: [
            { key: 'version_name', label: 'Package' },
            { key: 'size', label: 'Size' }
          ],
          key: sort.sortKey,
          sortingKey: sort.sortKey,
          direction: sort.sortDirection,
          onSortChange: (sort) => setSort({ sortKey: sort.key, sortDirection: sort.direction })
        }
      }}
      widths={['auto', 200]}
      body={{
        data: data,
        row: {
          spaced: true,
          render: [
            ({ version_name, id }) => <p 
              onClick={() => navigate(`/titles/${titleID}/content/${id}`)} 
              className='cursor-pointer text-primary-500'>{version_name}
            </p>,
            ({ size }) => humanFileSize(size)
          ]
        },
        empty: {
          title: 'No content!',
          text: 'This release has no content.',
          icon: 'movie'
        }
      }}
    />
  )

}

export default Content
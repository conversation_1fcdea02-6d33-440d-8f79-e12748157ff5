import React from 'react'
import { Icon } from '@bitcine/cinesend-theme'
import humanFileSize from "../../helpers/human_file_size";
import axios from "../../lib/axios";

function ContentVersions({release}) {
    const [expanded, setExpanded] = React.useState(false);
    return (
        <div className={'flex flex-col'}>
            <div className={'flex flex-row justify-between cursor-pointer'} onClick={() => setExpanded(!expanded)}>
                <p className={'text-sm text-primary-400'}>
                    {release.content.length} content versions.
                    ({release.content.length} DCPs, {humanFileSize(release.size)})</p>
                <Icon
                    className={'block float-right select-none'}
                    icon={expanded ? 'keyboard_arrow_up' : 'keyboard_arrow_down'}
                    onClick={() => setExpanded(!expanded)}
                />
            </div>
            <div>
            {expanded &&
                release?.content.map((content, index) => (
                <div className={'border-b-2 my-4'}>
                    <div className={'text-xs text-gray-400'}>{humanFileSize(content.size)}</div>
                    <div className={'text-sm text-black-600'}>{content.version_name}</div>
                </div>
                ))
            }
            </div>
        </div>
    )
}

export default ContentVersions
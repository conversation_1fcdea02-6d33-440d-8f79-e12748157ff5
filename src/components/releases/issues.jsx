import { convertToLocalDateTime } from '/src/helpers/convert_date'
import { Table } from '@bitcine/cinesend-theme'
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query'
import { useParams } from 'react-router-dom';
import { useState } from 'react';

const formatSnakeCase = (str) => {
  if (!str) return 'N/A'
  return str
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ')
}

function Issues() {
  const { releaseID } = useParams()
  const navigate = useNavigate()
  const apiUrl = `/api/releases/${releaseID}/issues`
  const headerDefaults = { search: '', page: 0, take: 15, sortBy: 'created_at', sortDirection: 'asc' }
  const defaults = localStorage.getItem(apiUrl) !== null
    ? JSON.parse(localStorage.getItem(apiUrl))
    : headerDefaults

  const [queries, setQueries] = useState(defaults)

  const params = new URLSearchParams(queries).toString()
  const url = `${apiUrl}?${params}`

  const setQuery = (data) => {
    const newQueries = { ...queries, ...data }
    if (data && !data.page) {
      newQueries.page = 0
    }
    setQueries(newQueries)
    localStorage.setItem(apiUrl, JSON.stringify(newQueries))
  }
  const { data, isLoading, error } = useQuery({ queryKey: [url] })
  const issues = data?.data

  return <Table
    status={{
      pending: isLoading,
      pendingMessage: 'Loading issues...',
      error: error
    }}
    header={{
      columns: [
        { text: 'Organization' },
        { text: 'Cinema' },
        { text: 'Publish Status', key: 'overall_status' },
        { text: 'Delivery Method', key: 'is_electronic' },
        { text: 'Delivery Requested By', key: 'deliver_at' },
        { text: 'Play Date', key: 'release_date' }
      ],
      searching: {
        search: queries.search,
        searchPlaceholder: 'Search...',
        onSearch: search => setQuery({ search }),
        rightIcon: {
          icon: 'close',
          onClick: () => setQuery({search: ''}),
          tooltip: {
            text: 'Clear search'
          }
        }
      },
      sorting: {
        options: [
          { key: 'overall_status', label: 'Publish Status' },
          { key: 'is_electronic', label: 'Delivery Method' },
          { key: 'deliver_at', label: 'Delivery Requested By' },
          { key: 'release_date', label: 'Play Date' }
        ],
        key: queries.sortBy,
        sortingKey: queries.sortBy,
        direction: queries.sortDirection,
        onSortChange: (sort) => setQuery({ sortBy: sort.key, sortDirection: sort.direction })
      }
    }}
    widths={[200, 200, 300, 200, 300, 300]}
    body={{
      data: issues,
      row: {
        onClick: (_, data) => navigate(`/bookings/${data.id}`),
        spaced: true,
        render: [
          ({ organization }) => organization.name,
          ({ cinema }) => cinema.name,
          ({ overall_status }) => <p className='text-error-500'>{formatSnakeCase(overall_status)}</p>,
          ({ is_electronic }) => (is_electronic ? 'E-Delivery' : 'Physical'),
          ({ deliver_at }) => convertToLocalDateTime(deliver_at),
          ({ release_date }) => convertToLocalDateTime(release_date)
        ]
      },
      empty: {
        title: 'No issues!',
        text: 'There are no issues related to this release.',
        icon: 'check'
      }
    }}
    paginate={{
      totalRows: data?.total,
      currentPage: data?.current_page - 1,
      rowsPerPage: data?.per_page,
      onPageChange: page => setQuery({ page }),
      onRowsPerPageChange: take => setQuery({ take })
    }}
  />
}

export default Issues
import React from 'react'
import {Icon} from '@bitcine/cinesend-theme'
import {convertToShorthand} from "/src/helpers/convert_date";

function ReleaseTerrestrialWorkflow({release}) {
    const [expanded, setExpanded] = React.useState(false);

    const CircleItem = (color, icon, title, subtext, isLast = false) =>
        <li className={`flex w-full items-center text-blue-600
            ${isLast ? '' : "sm:after:content-[''] after:w-full after:h-2 after:border-b after:border-gray-300 after:border-1 after:hidden sm:after:inline-block"}
            `}>
            <div className={`flex items-center justify-center w-8 h-8 border rounded-full shrink-0 ml-2 ${color}`}>
                <Icon
                    className={color}
                    icon={icon}/>
            </div>
            {title &&
            <div className={"ml-2 text-nowrap"}>
                <p className="text-xs text-primary-300">{title}</p>
                <p className="text-xs text-gray-400">{subtext}</p>
            </div>}
        </li>

    return (
        <div className={''}>
            <div className={''}>
                <ol className="w-full flex space-x-0 space-y-0">
                    <li className="w-full flex flex-row items-end text-blue-600 sm:after:content-[''] after:w-full after:h-2 after:hidden sm:after:inline-block">
                        <div className="border-t border-gray-300 rotate-45 relative top-[-36px] right-[-203px] w-[96px]"></div>
                    </li>
                    <li className="w-full flex items-center text-blue-600 sm:after:content-[''] after:w-full after:h-2 after:border-b after:border-gray-300 after:border-1 after:hidden sm:after:inline-block"></li>
                    <li className="w-full flex items-center text-blue-600 sm:after:content-[''] after:w-full after:h-2 after:border-b after:border-gray-300 after:border-1 after:hidden sm:after:inline-block"></li>
                    <li className="w-full flex items-center text-blue-600 sm:after:content-[''] after:w-full after:h-2 after:border-b after:border-gray-300 after:border-1 after:hidden sm:after:inline-block"></li>

                    {CircleItem(release.first_published_at ? 'success-delivery-step' : 'none-delivery-step',
                        release.first_published_at ? 'check' : '',
                        'First Publish',
                        convertToShorthand(release.first_published_at) ?? 'N/A')}

                    {CircleItem(release.last_published_at ? 'success-delivery-step' : 'none-delivery-step',
                        release.last_published_at ? 'check' : '',
                        'Last Publish',
                        convertToShorthand(release.last_published_at) ?? 'N/A',
                        true)}
                </ol>
            </div>
        </div>
    )
}

export default ReleaseTerrestrialWorkflow
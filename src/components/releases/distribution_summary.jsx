import { Icon } from '@bitcine/cinesend-theme'

function DistributionSummary({ ds }) {

  return (<>
    {ds ? <div className={'flex flex-row font-medium space-x-2'}>
      <div className='flex space-x-2 items-center'>
        <Icon tooltip={{ text: 'Satellite' }} icon='satellite_alt' className='text-md text-gray-500' />
        <p>{ds['satellite']}</p>
      </div>
      <small className='text-gray-300'>|</small>
      <div className='flex space-x-2 items-center'>
        <Icon tooltip={{ text: 'E-Delivery' }} icon='public' className='text-md text-gray-500' />
        <p>{ds['electronic']}</p>
      </div>
    </div> : 'None'}
  </>)
}

export default DistributionSummary;
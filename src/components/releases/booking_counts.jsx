import React from "react";

function BookingCounts ({booking_counts}) {
    return (
        (Object.values(booking_counts ?? []).reduce((partialSum, a) => partialSum + a, 0)
            ? <div className={'flex flex-row font-medium space-x-2'}>
                <div className={'text-blue-600 pr-2 border-r-2 border-gray-200'} title={'Booked'}>
                    {booking_counts['pending']}</div>
                <div className={'text-yellow-600 pr-2 border-r-2 border-gray-200'} title={'Pending'}>
                    {booking_counts['in_progress']}</div>
                <div className={'text-green-600 pr-2 border-r-2 border-gray-200'} title={'Completed'}>
                    {booking_counts['completed']}</div>
                <div className={'text-red-600 pr-2'} title={'Errors'}>{booking_counts['errors']}</div>
            </div>
            : 'None')
    )
}

export default BookingCounts;
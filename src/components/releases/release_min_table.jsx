import { Status, Table, Button, Modal, Input } from '@bitcine/cinesend-theme'
import dayjs from 'dayjs'
import React, { useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { convertToLocalDateTime, convertToShorthand } from '/src/helpers/convert_date'
import { useTitles } from '/src/hooks/titles'
import { useQuery } from '@tanstack/react-query'

const RELEASES_API = '/api/titles/:titleID/releases'

const ReleaseMiniTable = ({ title }) => {
  const { titleID } = useParams()

  const urlKey = RELEASES_API.replace(':titleID', titleID)
  const defaults = localStorage.getItem(urlKey) !== null
    ? JSON.parse(localStorage.getItem(urlKey))
    : { search: '', page: 0, take: 15, sortBy: 'created_at', sortDirection: 'DESC' }
  const [queries, setQueries] = useState(defaults)

  const params = new URLSearchParams(queries).toString()
  const url = `${urlKey}?${params}`

  const setQuery = (data) => {
    const newQueries = { ...queries, ...data }
    // if a page wasn't clicked, and anything else was changed reset page to 0.
    if (data && !data.page) {
      newQueries.page = 0
    }
    setQueries(newQueries)
    localStorage.setItem(urlKey, JSON.stringify(newQueries))
  }

  const { data: releases, error } = useQuery({ queryKey: [url] })
  const navigate = useNavigate()

  return (
    <>
      <div className="font-subheader text-[1.4rem] mx-1 font-normal pb-4">Bookings by Release</div>
      <Status pending={!releases}>
        <Table
          status={{
            pending: !releases?.data,
            pendingMessage: 'Loading Releases...',
            error: error
          }}
          header={{
            columns: [
              { text: 'Type', key: 'type' },
              { text: 'Name', key: 'package_name' },
              { text: 'Total Bookings' },
              { text: 'Booking Summary' },
              { text: 'Release Date' }
            ]
          }}
          widths={[200, 400, 100, 160, 'auto']}
          body={{
            data: releases?.data ?? [],
            row: {
              spaced: true,
              onClick: (event, data) => navigate(`/titles/${data.title_id}/releases/${data.id}`),
              render: [
                ({ types_list, type }) => (types_list.find((element) => element.value === type) ??
                  { label: 'n/a' }).label,
                ({ package_name }) => package_name,
                ({ booking_counts }) =>
                  (Object.values(booking_counts ?? []).reduce((partialSum, a) => partialSum + a, 0)
                    ? Object.values(booking_counts ?? []).reduce((partialSum, a) => partialSum + a, 0)
                    : 'None'),
                ({ booking_counts }) =>
                  (Object.values(booking_counts ?? []).reduce((partialSum, a) => partialSum + a, 0)
                    ? <div className={'flex flex-row font-medium space-x-2'}>
                      <div className={'text-blue-600 pr-2 border-r-2 border-gray-200'} title={'Booked'}>
                        {booking_counts['pending']}</div>
                      <div className={'text-yellow-600 pr-2 border-r-2 border-gray-200'} title={'Pending'}>
                        {booking_counts['in_progress']}</div>
                      <div className={'text-green-600 pr-2 border-r-2 border-gray-200'} title={'Completed'}>
                        {booking_counts['completed']}</div>
                      <div className={'text-red-600 pr-2'} title={'Issues'}>{booking_counts['errors']}</div>
                    </div>
                    : 'None'),
                (data) => convertToShorthand(dayjs(title.release_date).tz('GMT'))
              ]
            },
            empty: {
              title: 'No Releases have been created yet.',
              text: 'Create a release to get started.',
              icon: 'movie'
            }
          }}
          paginate={{
            totalRows: releases?.total,
            currentPage: releases?.current_page - 1,
            rowsPerPage: releases?.per_page,
            onPageChange: page => setQuery({ page }),
            onRowsPerPageChange: take => setQuery({ take })
          }}
        />
      </Status>
    </>
  )
}

export default ReleaseMiniTable

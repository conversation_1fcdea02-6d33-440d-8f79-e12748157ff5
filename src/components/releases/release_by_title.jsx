import { Status, Select, Button, Modal, Input, FileUpload } from '@bitcine/cinesend-theme'
import React, { useState } from 'react'
import { useParams } from 'react-router-dom'
import { useTitles } from '/src/hooks/titles'
import DashboardRow from '/src/components/dashboards/dashboard_row'
import TableCard from '/src/components/dashboards/table_card'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useAuth } from '/src/hooks/auth'

const RELEASES_API = '/api/titles/:titleID/releases/:releaseID'

const ReleaseByTitle = () => {
  const queryClient = useQueryClient()
  const { titleID, releaseID } = useParams()
  const { checkPermission } = useAuth()

  const url = RELEASES_API.replace(':titleID', titleID).replace(':releaseID', releaseID)
  const { updateRelease, prepareSatelliteRelease } = useTitles()

  const { data, isLoading } = useQuery({ queryKey: [url] })
  const mutate = () => queryClient.invalidateQueries({ queryKey: [url] })
  const [modal, setModal] = useState(false)
  const [newName, setNewName] = useState('')
  const [paramountId, setParamountId] = useState('')
  const [lionsgateId, setLionsgateId] = useState('')
  const [dchubVersionId, setDchubVersionId] = useState('')
  const [newReleaseType, setNewReleaseType] = useState(127)

  const removeButton = (data) => (
    <Button
      tertiary={true}
      disabled={!checkPermission('update-titles')}
      onClick={() => {
        let update = { remove_content_id: data }
        updateRelease({
          titleID,
          releaseID,
          onComplete: () => {
            mutate()
          },
          ...update,
        })
      }}
    >
      Remove
    </Button>
  )

  const addButton = (data) => (
    <Button
      tertiary={true}
      disabled={!checkPermission('update-titles')}
      onClick={() => {
        let update = { add_content_id: data }
        updateRelease({
          titleID,
          releaseID,
          onComplete: () => {
            mutate()
          },
          ...update,
        })
      }}
    >
      Add
    </Button>
  )

  const nameAndCpl = (name) => (
    <div className={'flex flex-col'}>
      <div>{name}</div>
      <div className={'font-extralight text-sm'}>
        {data?.data?.content.map((item) => {
          if (item.version_name === name) {
            return item.cpl_uuid
          }
          return false
        })}
        {data?.data?.content_available.map((item) => {
          if (item.version_name === name) {
            return item.cpl_uuid
          }
          return false
        })}
      </div>
    </div>
  )

  return (
    <Status pending={isLoading}>
      <div className={'flex justify-between'}>
        <h4>{data?.data?.package_name}</h4>
        <div className={'space-x-2'}>
          <Button
            icon={'satellite_alt'}
            disabled={
              data?.data?.content.length !== 1 || data?.data?.is_fazzt === 1 || !checkPermission('update-titles')
            }
            onClick={() => {
              prepareSatelliteRelease({
                titleID,
                releaseID,
                onComplete: () => {
                  mutate()
                },
              })
            }}
          >
            Prepare for Satellite
          </Button>
          <Button
            icon={'edit'}
            disabled={!checkPermission('update-titles')}
            onClick={() => {
              setNewName(data?.data?.package_name)
              setNewReleaseType(data?.data?.type)
              setParamountId(data?.data?.paramount_content_id)
              setLionsgateId(data?.data?.lionsgate_content_id)
              setDchubVersionId(data?.data?.dchub_version_id)
              setModal(true)
            }}
          >
            Edit Release Metadata
          </Button>
        </div>
      </div>

      <DashboardRow rowTitle={'Release Contents'}>
        <TableCard
          headers={[
            { isVisible: true, key: 'version_name', label: 'Content', processor: (data) => nameAndCpl(data) },
            { isVisible: true, key: 'id', label: '', processor: (data) => removeButton(data), width: 'w-1/5' },
          ]}
          values={data?.data?.content ?? []}
        />
      </DashboardRow>

      <DashboardRow rowTitle={'Available Contents'}>
        <TableCard
          headers={[
            { isVisible: true, key: 'version_name', label: 'Content', processor: (data) => nameAndCpl(data) },
            { isVisible: true, key: 'id', label: '', processor: (data) => addButton(data), width: 'w-1/5' },
          ]}
          values={data?.data?.content_available ?? []}
        />
      </DashboardRow>

      <DashboardRow rowTitle={'Ingest Letter'}>
        {checkPermission('update-titles') && (
          <FileUpload
            className={'w-1/4'}
            includeRemoveButton={false}
            upload={{
              message: 'Drop Letter PDF here',
              accept: 'application/pdf',
              icon: 'picture_as_pdf',
              apiURL: `${import.meta.env.VITE_BACKEND_URL}/api/releases/upload-letter/${releaseID}`,
              onComplete: (file, destinationUrl) => {
                //
                mutate()
              },
            }}
            button={{
              text: 'Upload',
            }}
          />
        )}

        {data?.data?.ingest_letter_url ? (
          <div className={'p-4 w-1/2 flex flex-col'}>
            <p className={'grow'}>
              Letter uploaded. This letter will be emailed when a new booking is received for this release. It will be
              sent to all sites that have a contact configured. Upload a new letter to replace the existing one.
            </p>
            <a
              className='underline text-primary-500 bottom'
              target='_blank'
              href={data?.data?.ingest_letter_url}
              rel='noreferrer'
            >
              View Current Letter
            </a>
          </div>
        ) : (
          ''
        )}
      </DashboardRow>

      {modal && (
        <Modal
          header={'Update Release Name'}
          onClose={() => {
            setModal(false)
            setNewName('')
            setNewReleaseType(127)
          }}
          confirmButton={{
            text: 'Update Release Metadata',
            onClick: () => {
              let data = {
                package_name: newName,
                type: newReleaseType,
                paramount_content_id: paramountId,
                lionsgate_content_id: lionsgateId,
                dchub_version_id: dchubVersionId,
              }
              updateRelease({
                onComplete: () => {
                  setModal(false)
                  setNewName('')
                  setParamountId('')
                  setLionsgateId('')
                  setDchubVersionId('')
                  mutate()
                },
                titleID,
                releaseID,
                ...data,
              })
            },
            disabled: !newName,
          }}
        >
          <div className={'flex-col space-y-4'}>
            <Input label={'Release Name'} value={newName} onChange={(e) => setNewName(e.target.value)} />
            <Input
              label={'Paramount Content ID'}
              message={'Required to match Paramount content orders.'}
              value={paramountId}
              onChange={(e) => setParamountId(e.target.value)}
            />
            <Input
              label={'Lionsgate Content ID'}
              message={'Required to match Lionsgate content orders.'}
              value={lionsgateId}
              onChange={(e) => setLionsgateId(e.target.value)}
            />
            <Input
              label={'DC Hub Version ID'}
              message={'Required to match DC Hub content orders.'}
              value={dchubVersionId}
              onChange={(e) => setDchubVersionId(e.target.value)}
            />
            <Select
              label={'Release Type'}
              className={'w-full'}
              options={data?.data?.types_list}
              value={data?.data?.types_list.find((types) => types.value === newReleaseType)}
              onChange={(value) => {
                setNewReleaseType(value.value)
              }}
            />
          </div>
        </Modal>
      )}
    </Status>
  )
}

export default ReleaseByTitle

import React, {useEffect, useState} from 'react'
import {Icon} from '@bitcine/cinesend-theme'
import axios from "../../lib/axios";

function TogglePin({release}) {
    const [pinned, setPinned] = useState(release.is_pinned);

    return (
        <>
            <Icon
                onClick={() => {
                    setPinned(!pinned)
                    axios.get(`api/releases/${release.id}/toggle-pin`)
                }}
                className={pinned ? 'text-primary-300' : 'text-neutral-200'}
                icon={'push_pin'}/>
        </>
    )
}

export default TogglePin
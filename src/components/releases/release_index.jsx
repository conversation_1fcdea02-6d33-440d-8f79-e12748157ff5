import { Select, Status, Table, Button, Modal, Input } from '@bitcine/cinesend-theme'
import React, { useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { convertToLocalDateTime } from '/src/helpers/convert_date'
import { useTitles } from '/src/hooks/titles'
import { useAuth } from '/src/hooks/auth'
import { useQuery, useQueryClient } from '@tanstack/react-query'

const RELEASES_API = '/api/titles/:titleID/releases'

const ReleaseTableIndex = () => {
  const { titleID } = useParams()
  const { createRelease, deleteRelease } = useTitles()
  const { checkPermission } = useAuth()

  const [modal, setModal] = useState(false)
  const [newName, setNewName] = useState('')
  const [paramountId, setParamountId] = useState(null)
  const [newReleaseType, setNewReleaseType] = useState(127)

  const urlKey = RELEASES_API.replace(':titleID', titleID)
  const defaults = localStorage.getItem(urlKey) !== null
    ? JSON.parse(localStorage.getItem(urlKey))
    : { search: '', page: 0, take: 15, sortBy: 'created_at', sortDirection: 'DESC' }
  const [queries, setQueries] = useState(defaults)
  const queryClient = useQueryClient()

  const params = new URLSearchParams(queries).toString()
  const url = `${urlKey}?${params}`

  const types_list = [
    {value: 1, label: 'Release - SMTPE'},
    {value: 2, label: 'Release - InterOp'},
    {value: 3, label: 'Release - LAS'},
    {value: 4, label: 'Release - Dolby Vision'},
    {value: 127, label: 'Other'}
  ]
  const setQuery = (data) => {
    const newQueries = { ...queries, ...data }
    // if a page wasn't clicked, and anything else was changed reset page to 0.
    if (data && !data.page) {
      newQueries.page = 0
    }
    setQueries(newQueries)
    localStorage.setItem(urlKey, JSON.stringify(newQueries))
  }

  const { data: releases, error } = useQuery({ queryKey: [url] })
  const mutate = () => queryClient.invalidateQueries({ queryKey: [url] })
  const navigate = useNavigate()

  return (
    <Status pending={!releases}>
      <Table
        customElement={[<Button disabled={!checkPermission('create-titles')} key={'button'}
          onClick={() => {setModal(true)}}>Create Release</Button>]}
        status={{
          pending: !releases?.data,
          pendingMessage: 'Loading Releases...',
          error: error
        }}
        header={{
          columns: [
            { text: 'Type', key: 'type' },
            { text: 'Name', key: 'package_name' },
            { text: 'Total Bookings' },
            { text: 'Booking Summary' },
            { text: 'Created', key: 'created_at' }
          ],
          searching: {
            search: queries.search,
            searchPlaceholder: 'Search...',
            onSearch: search => setQuery({ search }),
            rightIcon: {
              icon: 'close',
              onClick: () => setQuery({search: ''}),
              tooltip: {
                text: "Clear search"
              }
            }
          },
          sorting: {
            options: [
              { key: 'package_name', label: 'Name' },
              { key: 'created_at', label: 'Created Date' }
            ],
            key: queries.sortBy,
            sortingKey: queries.sortBy,
            direction: queries.sortDirection,
            onSortChange: (sort) => setQuery({ sortBy: sort.key, sortDirection: sort.direction })
          }
        }}
        widths={[200, 400, 100, 160, 'auto']}
        body={{
          data: releases?.data ?? [],
          row: {
            spaced: true,
            onClick: (event, data) => navigate(`/titles/${data.title_id}/releases/${data.id}`),
            render: [
              ({ types_list, type}) => (types_list.find((element) => element.value === type) ??
                { label: 'n/a' }).label,
              ({ package_name }) => package_name,
              ({ booking_counts }) =>
                (Object.values(booking_counts ?? []).reduce((partialSum, a) => partialSum + a, 0)
                  ? Object.values(booking_counts ?? []).reduce((partialSum, a) => partialSum + a, 0)
                  : 'None'),
              ({ booking_counts }) =>
                (Object.values(booking_counts ?? []).reduce((partialSum, a) => partialSum + a, 0)
                  ? <div className={'flex flex-row font-medium space-x-2'}>
                    <div className={'text-blue-600 pr-2 border-r-2 border-gray-200'} title={'Booked'}>
                      {booking_counts['pending']}</div>
                    <div className={'text-yellow-600 pr-2 border-r-2 border-gray-200'} title={'Pending'}>
                      {booking_counts['in_progress']}</div>
                    <div className={'text-green-600 pr-2 border-r-2 border-gray-200'} title={'Completed'}>
                      {booking_counts['completed']}</div>
                    <div className={'text-red-600 pr-2'} title={'Errors'}>{booking_counts['errors']}</div>
                  </div>
                  : 'None'),
              ({ created_at }) => convertToLocalDateTime(created_at)
            ]
          },
          empty: {
            title: 'No Releases have been created yet.',
            text: 'Create a release to get started.',
            icon: 'movie'
          }
        }}
        paginate={{
          totalRows: releases?.total,
          currentPage: releases?.current_page - 1,
          rowsPerPage: releases?.per_page,
          onPageChange: page => setQuery({ page }),
          onRowsPerPageChange: take => setQuery({ take })
        }}
      />
      {modal &&
        <Modal header={'Create a Release'}
          onClose={() => {
            setModal(false)
            setNewName('')
          }}
          confirmButton={{
            text: 'Create Release',
            onClick: () => {
              let data = {package_name: newName, type: newReleaseType, paramount_content_id: paramountId}
              createRelease({onComplete: () => {
                setModal(false)
                setNewName('')
                setParamountId(null)
                mutate()
              }, titleID, ...data})
            },
            disabled: !newName
          }}
        >
          <div className={'flex-col space-y-4'}>
            <Input label={'Release Name'}
              value={newName} onChange={(e) => setNewName(e.target.value)}/>
            <Input label={'Paramount Content ID'}
              message={'Required to match Paramount content orders.'}
              value={paramountId} onChange={(e) => setParamountId(e.target.value)}/>
            <Select
              label={'Release Type'}
              className={'w-full'}
              options={types_list}
              value={types_list.find(types => types.value === newReleaseType)}
              onChange={value => {
                setNewReleaseType(value.value)
              }}/>
          </div>
        </Modal>}
    </Status>
  )
}

export default ReleaseTableIndex

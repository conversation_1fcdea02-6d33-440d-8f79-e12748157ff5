import React from 'react'
import {Icon} from '@bitcine/cinesend-theme'
import {convertToShorthand} from "../../helpers/convert_date";

function ReleaseSatWorkflow({release}) {
    const [expanded, setExpanded] = React.useState(false);

    const CircleItem = (color, icon, title, subtext, isLast = false) =>
        <li className={`flex w-full items-center text-blue-600
            ${isLast ? '' : "sm:after:content-[''] after:w-full after:h-2 after:border-b after:border-gray-300 after:border-1 after:hidden sm:after:inline-block"}
            `}>
            <div className={`flex items-center justify-center w-8 h-8 border rounded-full shrink-0 ml-2 ${color}`}>
                <Icon
                    className={color}
                    icon={icon}/>
            </div>
            {title &&
                <div className={"ml-2 text-nowrap"}>
                    <p className="text-xs text-primary-300">{title}</p>
                <p className="text-xs text-gray-400">{subtext}</p>
            </div>}
        </li>

    return (
        <div className={''}>
            <div className={''}>
                <ol className="w-full flex space-x-0">
                    {CircleItem(release.size > 0 ? 'success-delivery-step' : 'pending-delivery-step',
                        release.size > 0 ? 'check' : 'schedule',
                        'Content Available', convertToShorthand(release.created_at))}

                    {CircleItem(
                        release.fazzt_info.ready_to_transmit  ? 'success-delivery-step' : 'pending-delivery-step',
                        release.fazzt_info.ready_to_transmit  ? 'check' : 'schedule',
                        'Ready to Transmit',
                        convertToShorthand(release.fazzt_info.ready_to_transmit) ?? 'N/A')}

                    {CircleItem(
                        release.fazzt_info.percent === 100 ? 'success-delivery-step' : 'none-delivery-step',
                        release.fazzt_info.percent === 100 ? 'check' : 'schedule',
                        `Transmitting ${release.fazzt_info.percent ? `(${release.fazzt_info.percent} %)` : ''}`,
                        convertToShorthand(release.fazzt_info.transfer_date))}

                    {CircleItem(
                        release.fazzt_info.delivered ? 'success-delivery-step' : 'none-delivery-step',
                        release.fazzt_info.delivered ? 'check' : 'schedule',
                        'Delivered',
                        convertToShorthand(release.fazzt_info.delivered) ?? 'N/A')}

                    {CircleItem(release.fazzt_info.first_published ? 'success-delivery-step' : 'none-delivery-step',
                        release.fazzt_info.first_published ? 'check' : '',
                        'First Publish',
                        convertToShorthand(release.fazzt_info.first_published) ?? 'N/A')}

                    {CircleItem(release.fazzt_info.last_published ? 'success-delivery-step' : 'none-delivery-step',
                        release.fazzt_info.last_published ? 'check' : '',
                        'Last Publish',
                        convertToShorthand(release.fazzt_info.last_published) ?? 'N/A',
                        true)}
                </ol>
            </div>
        </div>
    )
}

export default ReleaseSatWorkflow
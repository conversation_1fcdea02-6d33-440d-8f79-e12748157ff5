import Card from '/src/components/layouts/card'
import { Button, Icon } from '@bitcine/cinesend-theme'
import { useQuery } from '@tanstack/react-query'

const Dcps = () => {

  const apiUrl = '/api/fazzt/dcps'

  const { data, isValidating, errors } = useQuery({ queryKey: [apiUrl] })

  /**
   * This will have content.version with a version record when matched, otherwise details are empty.
   */
  return (
    <>
      <Card title={'DCPs on FAZZT'}>
        {data?.data && data?.data.map(content => (
          <div className={'flex flex-row justify-between'}>
            <div className={'w-1/2'}>{content.asset_uuid}</div>
            <div>{content.status}</div>
            <div>{content.updated_at}</div>
            <div><Button>Delete DCP</Button></div>
          </div>
        ))}
      </Card>
    </>
  )
}

export default Dcps

import Card from '/src/components/layouts/card'
import { useQuery } from '@tanstack/react-query'

const CommandLog = () => {

  const apiUrl = '/api/fazzt/command-log'

  const { data, isFetching, error } = useQuery({ queryKey: [apiUrl] })

  const completed = {
    '1' : <>Completed</>,
    '0' : <>Not yet Processed</>
  }

  return (
    <>
      <Card title={'Fazzt Command Log'}>
        {data?.data && data?.data.map(content => (
          <div className={'flex flex-row justify-between'}>
            <div className={'w-1/2'}>{content.method}</div>
            <div>{completed[content.is_completed]}</div>
            <div>{content.updated_at}</div>
          </div>
        ))}
      </Card>
    </>
  )
}

export default CommandLog

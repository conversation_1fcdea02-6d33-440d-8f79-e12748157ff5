import { useState } from 'react'
import dayjs from 'dayjs'
import { Table, Input, Modal, Button } from '@bitcine/cinesend-theme'
import { convertToLocalDateTime } from '/src/helpers/convert_date'
import useTransfers from './use_transfers'

const Transfers = () => {
  const [query, setQuery] = useState({})
  const [modalOpen, setModalOpen] = useState(false)
  const [cancelData, setCancelData] = useState({})
  const {
    handleDrag,
    activeTransfers,
    errors,
    updatePriorities,
    updatingPriorities,
    editingInput,
    handleBlur,
    cancelTransfer,
    deleteFromQueue,
  } = useTransfers()

  const onClose = () => {
    setModalOpen(false)
  }
  return (
    <>
      <Table
        status={{
          pending: !activeTransfers,
          pendingMessage: 'Loading Satellite Transfer Queue',
          error: errors,
        }}
        header={{
          columns: [
            { text: 'P', key: 'priority' },
            { text: '%', key: 'percent' },
            { text: 'Status', key: 'status' },
            { text: 'Package' },
            { text: 'Start Time' },
            { text: 'Last Updated Date', key: 'updated_at' },
          ],
          searching: {
            search: query.search,
            searchPlaceholder: 'Search...',
            onSearch: (search) => setQuery({ search }),
            rightIcon: {
              icon: 'close',
              onClick: () => setQuery({ search: '' }),
              tooltip: {
                text: 'Clear search',
              },
            },
          },
          sorting: {
            options: [],
            key: query.sortBy,
            sortingKey: query.sortBy,
            direction: query.sortDirection,
            onSortChange: (sort) => setQuery({ sortBy: sort.key, sortDirection: sort.direction }),
          },
        }}
        draggable={true}
        widths={[80, 40, 100, 200, 100, 90]}
        body={{
          onDragEnd: handleDrag,
          data: activeTransfers?.data,
          row: {
            spaced: true,
            // Pass these as props to the row and get them from the render function. This ensures they are not stale.
            // Hacky but necessary until components inside the table are handled correctly
            transfers: activeTransfers,
            updatePriorities: updatePriorities,
            editingInput: editingInput,
            updatingPriorities: updatingPriorities,
            render: [
              ({ priority, version, id }, index, { transfers, updatePriorities, updatingPriorities }) =>
                priority === 0 ? (
                  <Button
                    className={'w-full'}
                    onClick={() => {
                      setCancelData(version)
                      setModalOpen(true)
                    }}
                  >
                    Cancel
                  </Button>
                ) : (
                  <Input
                    value={priority}
                    disabled={updatingPriorities || priority === 0}
                    onChange={(e) => updatePriorities(e.target.value, index)}
                    onBlur={() => handleBlur(transfers.data, index)}
                    // type={'number'}
                    // min={1}
                    // max={transfers?.total}
                  />
                ),
              ({ priority, percent }) => (priority === 0 ? `${percent}%` : ''),
              ({ state }) => state,
              ({ asset_uuid, version }) => (version ? version.version_name : asset_uuid),
              ({ start_time }) => convertToLocalDateTime(start_time ?? dayjs()),
              ({ updated_at }) => convertToLocalDateTime(updated_at),
            ],
          },
          empty: {
            title: 'No satellite transfers found.',
            text: 'Please refine your filters.',
            icon: 'satellite',
          },
        }}
        paginate={{
          totalRows: activeTransfers?.total,
          currentPage: activeTransfers?.current_page - 1,
          rowsPerPage: activeTransfers?.per_page,
          onPageChange: (page) => setQuery({ page }),
          onRowsPerPageChange: (take) => setQuery({ take }),
        }}
      />
      {modalOpen && (
        <Modal
          header='Cancel Active Transfer'
          onClose={onClose}
          confirmButton={{
            type: 'error',
            text: 'Confirm Cancel',
            onClick: () => {
              cancelTransfer()
              onClose()
            },
          }}
        >
          <div className='flex flex-col space-y-4'>
            <p>Are you sure you want to cancel {cancelData?.version_name}?</p>
            <p>All transfers in queue will be moved up immediately.</p>
          </div>
        </Modal>
      )}
    </>
  )
}

export default Transfers

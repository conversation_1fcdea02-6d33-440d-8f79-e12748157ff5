import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import axios from '/src/lib/axios'
import { useSnackbar } from '/src/hooks/snackbar'
import { useState } from 'react'
import { data } from 'autoprefixer'

const useTransfers = () => {
  const apiUrl = '/api/fazzt/active-transfers'
  const priorityUrl = '/api/fazzt/set-priority'
  const { addMessage } = useSnackbar()
  const queryClient = useQueryClient()
  const { data: activeTransfers, errors } = useQuery({ queryKey: [apiUrl], refetchInterval: 60000 })

  const invalidateTransfers = () => {
    queryClient.invalidateQueries({ queryKey: [apiUrl] })
  }

  const updatePriorities = (newPriority, index) => {
    // disable the other inputs to avoid onBlur shenanigans
    const obj = activeTransfers.data[index]
    const updatedObj = { ...obj, priority: newPriority }
    const updatedTransfers = activeTransfers.data.map((item, i) => (i === index ? updatedObj : item))
    queryClient.setQueryData([apiUrl], { ...activeTransfers, data: updatedTransfers })
  }

  const handleDrag = (priorities) => {
    setPriority.mutate(
      priorities.map((obj, index) => ({
        priority: index,
        id: obj.id,
      }))
    )
  }

  const handleBlur = (transfers, index) => {
    const item = transfers[index]
    const newPriority = Number(item.priority)
    const updatedTransfers = [...transfers]

    if (newPriority === index) return

    // Handle bad priorites
    if (isNaN(newPriority) || newPriority === 0 || !newPriority) {
      addMessage('Priority should be a number, and cannot be 0.', 'error')
      queryClient.setQueryData([apiUrl], {
        ...activeTransfers,
        data: updatedTransfers.map((transfer) => {
          if (transfer.id === item.id) return { ...transfer, priority: index }
          return transfer
        }),
      })
      return
    }

    // If the priority will be inserted at an out of bounds index, shove it at the end
    if (newPriority > transfers.length - 1) {
      updatedTransfers.splice(index, 1)
      updatedTransfers.push(item)
      queryClient.setQueryData([apiUrl], { ...activeTransfers, data: updatedTransfers })
      setPriority.mutate(
        updatedTransfers.map((obj, index) => ({
          priority: index,
          id: obj.id,
        }))
      )
      return
    }

    // Otherwise move it to the appropriate index and shift everything else down
    updatedTransfers.splice(index, 1)
    updatedTransfers.splice(newPriority, 0, item)
    setPriority.mutate(
      updatedTransfers.map((obj, index) => ({
        priority: index,
        id: obj.id,
      }))
    )
  }

  const setPriority = useMutation({
    mutationFn: (priorities) => axios.post(`${priorityUrl}`, priorities),
    onSuccess: (res) => {
      addMessage('Priority updated!')
      queryClient.setQueryData([apiUrl], res.data)
    },
    onError: (error) => {
      const { response } = error
      const { data: resData, status } = response

      let errMsg = 'Error updating priority.'

      switch (status) {
        case 400:
        case 404:
          errMsg = resData.message
          queryClient.setQueryData([apiUrl], resData.data)
          break
        default:
          invalidateTransfers()
          break
      }
      addMessage(errMsg, 'error')
    },
  })

  const cancelTransfer = () => {
    // cancel the only active transfer.
    axios
      .post('/api/fazzt/cancel-transfer', {})
      .then((res) => {
        // update res data with recalculated transfers from api
        queryClient.setQueryData([apiUrl], res.data)
      })
      .catch((error) => {
        addMessage('Failed to cancel transfer.', 'error')
      })
      .finally(() => {})
  }

  const deleteFromQueue = (id) => {
    // delete an item from the queue
    axios
      .post('/api/fazzt/delete-from-queue', {
        deleteId: id,
      })
      .then((res) => {
        // update res data with recalculated transfers from api
        queryClient.setQueryData([apiUrl], res.data)
      })
      .catch((error) => {
        addMessage('Failed to delete from queue.', 'error')
      })
      .finally(() => {})
  }

  return {
    handleDrag,
    activeTransfers,
    errors,
    updatePriorities,
    updatingPriorities: setPriority.isPending,
    handleBlur,
    invalidateTransfers,
    cancelTransfer,
    deleteFromQueue,
  }
}

export default useTransfers

import {useQuery} from '@tanstack/react-query'
import dayjs from "dayjs";
import {useState} from "react";
import {Table} from '@bitcine/cinesend-theme'
import {convertToLocal, convertToLocalDateTime} from '/src/helpers/convert_date'

const History = () => {

    const apiUrl = '/api/fazzt/completed-transfers'

    const {data, isValidating, errors} = useQuery({queryKey: [apiUrl]})
    const [queries, setQueries] = useState({})

    return (
        <Table
            status={{
                pending: !data,
                pendingMessage: 'Loading Satellite Transfer Queue',
                error: errors
            }}
            header={{
                columns: [
                    {text: 'Package'},
                    {text: 'Start Time'},
                    {text: 'Actual Start Time'},
                    {text: 'Completed Time'},
                    {text: 'Last Updated Date', key: 'updated_at'}
                ],
                searching: {
                    search: queries.search,
                    searchPlaceholder: 'Search...',
                    onSearch: search => setQuery({search}),
                    rightIcon: {
                        icon: 'close',
                        onClick: () => setQuery({search: ''}),
                        tooltip: {
                            text: "Clear search"
                        }
                    }
                },
                sorting: {
                    options: [],
                    key: queries.sortBy,
                    sortingKey: queries.sortBy,
                    direction: queries.sortDirection,
                    onSortChange: (sort) => setQuery({sortBy: sort.key, sortDirection: sort.direction})
                },
            }}
            widths={[200, 90, 90, 90, 90]}
            body={{
                data: data?.data,
                row: {
                    spaced: true,
                    render: [
                        ({asset_uuid, version}) => version ? version.version_name : asset_uuid,
                        ({start_time}) => convertToLocalDateTime(start_time ?? dayjs()),
                        ({actual_start_at}) => convertToLocalDateTime(actual_start_at ?? dayjs()),
                        ({actual_completed_at}) => convertToLocalDateTime(actual_completed_at ?? dayjs()),
                        ({updated_at}) => convertToLocalDateTime(updated_at)
                    ]
                },
                empty: {
                    title: 'No satellite transfers found.',
                    text: 'Please refine your filters.',
                    icon: 'satellite'
                }
            }}
            paginate={{
                totalRows: data?.total,
                currentPage: data?.current_page - 1,
                rowsPerPage: data?.per_page,
                onPageChange: page => setQuery({page}),
                onRowsPerPageChange: take => setQuery({take})
            }}
        />
    )
}

export default History

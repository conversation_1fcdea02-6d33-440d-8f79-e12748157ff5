import Select from '@bitcine/cinesend-theme/dist/select'
import React, { useState, useEffect } from 'react'
import { Button, Input } from '@bitcine/cinesend-theme'
import { useUsers } from '/src/hooks/users'
import ErrorMessages from '/src/components/error_messages'
import SelectOrganization from '/src/components/organizations/select_organization'
import getUpdatedData from '/src/helpers/get_updated_data'
import { useAuth } from '/src/hooks/auth'
import { statuses } from './statuses'
import SelectRole from './select_role'
import groupRoles from './group_roles'

const Details = ({ user, mutationKey }) => {
  const [errors, setErrors] = useState([])
  const [data, setData] = useState(user)
  const [pending, setPending] = useState(false)
  const { checkRole, checkPermission } = useAuth()
  const showRoleSelect = checkRole('admin') || checkRole('super-admin')
  const role = groupRoles.find(({ value }) => value === data.role) || null
  const canUpdate = checkPermission('update-users')

  useEffect(() => {
    setData(user)
  }, [user])

  const update = newData => {
    setData({ ...data, ...newData, setErrors })
  }
  const { updateUser, deleteUser, resendInvite } = useUsers()
  const save = () => {
    setPending(true)
    updateUser({
      userID: user.id,
      ...getUpdatedData(user, data),
      setErrors: errors => {
        setPending(false)
        setErrors(errors)
      }
    })
  }
  return (
    <div className="flex flex-col space-y-4 max-w-2xl">
      <Input
        label="Full Name"
        value={data.name}
        disabled={!canUpdate}
        onChange={e => update({ name: e.target.value })}
      />
      <Input
        label="Email"
        value={data.email}
        disabled={!canUpdate}
        onChange={e => update({ email: e.target.value })}
      />
      <SelectOrganization
        value={data.organization_id}
        disabled={!canUpdate}
        onChange={organization_id => update({ organization_id })}/>
      <Select
        label={'Status'}
        value={statuses.find(opt => opt.value === data.status)}
        disabled={!canUpdate}
        options={statuses}
        onChange={value => {
          update({ status: value.value })
        }}
      />
      {showRoleSelect && <SelectRole role={role} onChange={role => update({ role: role.value })}/>}
      <div className="flex justify-between">
        {checkPermission('delete-users') ?
          <Button
            onClick={() => {
              if (window.confirm('Are you sure you want to permanently delete this user?')) {
                deleteUser({ setErrors, userID: data.id })
              }
            }}
            type={'error'}>
            Delete User</Button> : <>&nbsp;</>}

        {checkPermission('create-users') && data.status === 'pending' ?
          <Button
            onClick={() => {
              resendInvite({ setErrors, userID: data.id })
            }}
            type={'info'}>
            Resend Invite</Button> : <>&nbsp;</>}

        {canUpdate && <Button
          onClick={save}
          disabled={JSON.stringify(data) === JSON.stringify(user) || pending}>
          Save
        </Button>}
      </div>
      <ErrorMessages errors={errors}/>
    </div>
  )

}

export default Details

import { Table, Modal } from '@bitcine/cinesend-theme'
import { convertToLocalDateTime } from '/src/helpers/convert_date'
import { useState } from 'react'
import { useNavigate } from 'react-router-dom'

const Details = ({ user }) => {
  const navigate = useNavigate()

  const [showModal, setShowModal] = useState(false)
  const [modalData, setModalData] = useState({ 'header': 'title of modal' })
  const modalClose = () => setShowModal(false)

  const modelMapper = [
    { model: 'App\\Models\\User', url: '/users/%d', label: 'user', nameField: 'name' },
    { model: 'App\\Models\\Organization', url: '/organizations/%d', label: 'organization', nameField: 'name' },
    { model: 'App\\Models\\CinemaSite', url: '/sites/%d', label: 'cinema', nameField: 'name' },
    { model: 'App\\Models\\Booking', url: '/bookings/%d', label: 'booking', nameField: 'label' },
    { model: 'App\\Models\\Title', url: '/titles/%d', label: 'title', nameField: 'friendly_title' },
    { 
      model: 'App\\Models\\Version',
      url: '/titles/%d/versions',
      label: 'version',
      nameField: 'nickname',
      parentField: 'title_id'
    }
  ]

  const getModelByType = (type) => modelMapper.find(element => element.model === type)

  const descriptionByModel = (activityLog) => {
    let modelMap = getModelByType(activityLog.subject_type)
    if (!modelMap) {
      return <>{activityLog.description}</>
    }
    return <>
      <span className='capitalize'>{activityLog.event}</span> {modelMap.label}&nbsp;
      {activityLog.subject && <button className={'text-primary-600 underline'} onClick={() => {
        let url = modelMap.url.replace('%d', modelMap.parentField
          ? activityLog.subject[modelMap.parentField]
          : activityLog.subject_id)
        navigate(url)
      }}>{activityLog.subject[modelMap.nameField]}</button>}
    </>
  }

  const linkToMoreDetails = (activityLog) => {
    if (Object.keys(activityLog.properties).length === 0) {
      return <></>
    }

    return <button
      onClick={() => showModalWithDetails(activityLog)}
      className={'text-small text-gray-500 hover:text-primary-500'}>
      &raquo; view details</button>
  }

  const getModalContents = (activityLog) => {
    if (activityLog.properties['old'] && activityLog.properties['attributes']) {
      return <>
        <div className='font-bold'>Changes</div>
        {Object.entries(activityLog.properties['old']).map(([key, value]) =>
          <div className={'flex flex-row justify-between'}>
            {key}: {value}{' => '}{activityLog.properties['attributes'][key]}
          </div>)}
      </>
    }
    if (activityLog.properties['ip']) {
      return <>From: {activityLog.properties['ip']}<br/>Browser: {activityLog.properties['agent']}</>
    }
    return <>{descriptionByModel(activityLog)}</>
  }
  const getHeaderContents = (activityLog) => {
    if (activityLog.event) {
      return <div className='first-letter:capitalize'>
        {activityLog.event} {getModelByType(activityLog.subject_type)?.label}
      </div>
    }
    return activityLog.description
  }

  const showModalWithDetails = (activityLog) => {
    setModalData({
      ...modalData,
      header: getHeaderContents(activityLog),
      body: () => getModalContents(activityLog)
    })
    setShowModal(true)
  }

  return (<>
    <Table
      header={{
        columns: [
          { text: 'Description' },
          { text: 'Activity Date' }
        ]
      }}
      widths={['auto', 300]}
      body={{
        data: user?.activities ?? [],
        row: {
          spaced: true,
          render: [
            (row) => <>{descriptionByModel(row)} {linkToMoreDetails(row)}</>,
            ({ created_at }) => convertToLocalDateTime(created_at)
          ]
        },
        empty: {
          title: 'No activity logs found!',
          icon: 'people'
        }
      }}
    />
    {showModal && <Modal
      onClose={modalClose}
      {...modalData}>
      {modalData.body()}
    </Modal>}
  </>)
}

export default Details

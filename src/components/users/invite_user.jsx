import { Button, Modal, Input } from '@bitcine/cinesend-theme'
import React, { useState } from 'react'
import { useUsers } from '/src/hooks/users'
import ErrorMessages from '/src/components/error_messages'
import SelectOrganization from '/src/components/organizations/select_organization'
import SelectRole from './select_role'
import { useAuth } from '/src/hooks/auth'

const InviteUser = ({ orgId }) => {
  const [errors, setErrors] = useState([])
  const [open, setOpen] = useState(false)
  const [name, setName] = useState('')
  const [email, setEmail] = useState('')
  const [organizationID, setOrganizationID] = useState(orgId)
  const [pending, setPending] = useState(false)
  const [role, setRole] = useState(null)
  const { createUser } = useUsers()
  const { checkRole, checkPermission } = useAuth()
  const showRoleSelect = checkRole('admin') || checkRole('super-admin')

  const inviteUser = () => {
    setPending(true)
    createUser({
      name,
      email,
      organization_id: organizationID,
      role: role?.value ?? undefined,
      onComplete: () => {
        setOpen(false)
      },
      setErrors: errors => {
        setPending(false)
        setErrors(errors)
      }
    })
  }
  return (
    <>
      {checkPermission('create-users') && <Button icon='add' onClick={() => setOpen(true)}>Add user</Button>}
      {open ?
        <Modal
          header='Add User'
          onClose={() => setOpen(false)}
          pending={pending}
          confirmButton={{
            text: 'Add user',
            onClick: () => inviteUser(),
            disabled: !name || !email || !organizationID
          }}>
          <div className='flex flex-col space-y-4'>
            <SelectOrganization disabled={orgId ?? false}
              value={organizationID} onChange={organizationID => setOrganizationID(organizationID)}/>
            <Input label='Full name' value={name} onChange={e => setName(e.target.value)}/>
            <Input label='Email address' value={email} onChange={e => setEmail(e.target.value)}/>
            {showRoleSelect && <SelectRole role={role} onChange={role => setRole(role)}/>}
            <ErrorMessages errors={errors}/>
          </div>
        </Modal> : null}
    </>
  )
}

export default InviteUser

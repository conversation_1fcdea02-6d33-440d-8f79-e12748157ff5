import React, { useState } from 'react'
import { useUsers } from '/src/hooks/users'
import ErrorMessages from '/src/components/error_messages'
import { Table, Input, Button } from '@bitcine/cinesend-theme'
import { convertToLocalDateTime } from '/src/helpers/convert_date'
import { useAuth } from '/src/hooks/auth'
import { useQuery, useQueryClient } from '@tanstack/react-query'

const ApiTokens = ({ user }) => {
  const [errors, setErrors] = useState([])
  const [lastToken, setLastToken] = useState({})
  const [name, setName] = useState('')
  const { createToken, deleteToken } = useUsers()
  const queryClient = useQueryClient()

  const { checkPermission } = useAuth()
  const TOKENS_URL = '/api/users/{userId}/tokens'
  const dataUrl = TOKENS_URL.replace('{userId}', user.id)
  const { data, isFetching, error } = useQuery({ queryKey: [dataUrl]})
  const mutate = () => queryClient.invalidateQueries({ queryKey: [dataUrl]})
  
  const newToken = () => {
    if (name) {
      createToken({
        setErrors, userID: user.id, onComplete: (data) => {
          setLastToken(data)
          setName('')
          mutate()
        }, name
      })
    }
  }

  const byeToken = (token) => {
    deleteToken({
      setErrors, userID: user.id, tokenID: token.id, onComplete: () => {
        mutate()
      }
    })
  }

  return (
    <div>
      <div className="flex flex-col space-y-4 max-w-2xl">
        {lastToken?.message && <>
          <p>{lastToken.message}</p>
          <Input value={lastToken.token} disabled={true} copyIcon={true}/>
        </>}

        <div className="flex flex-col space-y-2">
          <h5>Create a New Token</h5>
          <Input disabled={!checkPermission('update-users')}
            label="Descriptive Name" value={name} onChange={(e) => setName(e.target.value)}/>
          <Button disabled={!name} className="w-1/2" onClick={() => newToken()}>Create a New Token</Button>
        </div>
      </div>

      <Table
        status={{
          pending: isFetching,
          pendingMessage: 'Loading tokens...',
          error: error
        }}
        header={{
          columns: [
            { text: 'Descriptive Name' },
            { text: 'Last Used' },
            { text: 'Created Date' },
            { text: 'Delete' }
          ]
        }}
        widths={['auto', 300, 300, 120]}
        body={{
          data: data?.tokens ?? [],
          row: {
            spaced: true,
            render: [
              ({ name }) => name,
              ({ last_used_at }) => convertToLocalDateTime(last_used_at),
              ({ created_at }) => convertToLocalDateTime(created_at),
              (token) => <Button disabled={!checkPermission('update-users')}
                onClick={() => byeToken(token)}>Delete</Button>
            ]
          },
          empty: {
            title: 'No Tokens found!',
            text: '',
            icon: 'url'
          }
        }}
      />

      <ErrorMessages errors={errors}/>
    </div>
  )
}

export default ApiTokens

import { useState } from 'react'
import { InputArea, Button } from '@bitcine/cinesend-theme'
import axios from '/src/lib/axios'

function AddNote ({ modelName, modelId, callback = () => {} }) {
  const [note, setNote] = useState('')
  const [disabled, setDisabled] = useState(false)
  const POST_NOTE_URL = `/api/notes/${modelName}/${modelId}`

  return (
    <>
      <h5>Notes</h5>
      <InputArea
        disabled={disabled}
        value={note}
        onChange={(e) => setNote(e.target.value)}
      />
      <Button
        disabled={disabled}
        className={'w-1/4'}
        onClick={() => {
          // disable the form
          setDisabled(true)
          axios.post(POST_NOTE_URL, {
            note
          }).then(res => {
            // clear the form
            setNote('')
          }).catch(error => {}).finally(() => {
            // show an error
          }).finally(() => {
            // eanble the form
            setDisabled(false)
            callback()
          })
        }}
      >Save note</Button>
    </>

  )
}

export default AddNote

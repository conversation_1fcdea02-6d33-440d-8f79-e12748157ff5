import { Avatar, Icon, InputArea, Button } from '@bitcine/cinesend-theme'
import { useState } from 'react'
import { convertToLocalDateTime } from '/src/helpers/convert_date'
import { v4 as uuid } from 'uuid'
import { useAuth } from '/src/hooks/auth'
import { useSnackbar } from '/src/hooks/snackbar'
import axios from '/src/lib/axios'
import AddNote from './add_note'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

function EditBox ({noteId, note, callback = () => {}}) {
  let { addMessage } = useSnackbar()

  const [editNote, setEditNote] = useState(note)
  const [disabled, setDisabled] = useState(false)

  const PUT_NOTE_URL = '/api/notes/' // + noteID
  const saveNote = () => {
    setDisabled(true)
    let url = PUT_NOTE_URL + noteId
    axios.put(url, { note: editNote }).then(() => {
      addMessage('Note updated!')
      setEditNote('')
    }).catch(error => {
      addMessage('Permission denied.', 'error')
    }).finally(() => {
      setDisabled(false)
      callback()
    })
  }

  return (
    <div className={'w-4/5'}>
      <InputArea
        disabled={disabled}
        value={editNote}
        onChange={(e) => setEditNote(e.target.value)}
      />
      <Button
        disabled={disabled}
        className={'w-1/4'}
        onClick={() => saveNote()}
      >Update note</Button>
    </div>
  )
}
function NotesTable ({ modelName, modelId }) {

  let { addMessage } = useSnackbar()
  const { user } = useAuth()
  const queryClient = useQueryClient()

  const GET_NOTES_URL = `/api/notes/${modelName}/${modelId}`
  const DELETE_NOTE_URL = '/api/notes/' // + noteID

  const { data: notes } = useQuery({ queryKey: [GET_NOTES_URL] })
  const mutate = () => queryClient.invalidateQueries({ queryKey: [GET_NOTES_URL]})
  const [noteId, setNoteId] = useState(0)

  const deleteNote = useMutation({
    mutationFn: (noteID) => axios.delete(`${DELETE_NOTE_URL}${noteID}`),
    onMutate: async (noteID) => {
      if (!window.confirm('Are you sure you want to delete this note?')) {
        throw new Error('Deletion canceled')
      }
    },
    onSuccess: () => {
      addMessage('Note deleted!')
      queryClient.invalidateQueries([GET_NOTES_URL])
    },
    onError: () => {
      addMessage('Permission denied.', 'error')
    }
  })

  const updateNote = (noteId, note) => {
    setNoteId(noteId)
  }

  return (
    <>
      <AddNote modelName={modelName} modelId={modelId} callback={() => mutate()}/>

      {notes?.data && notes.data.map(note => (
        <div key={uuid()} className={'card-box'}>
          <div className={'flex flex-row justify-items-stretch'}>
            <div className={'w-3/5 text-md pr-4'}>
              <div className={'float-right flex-row flex space-x-1'}>
                {(note.user_id === user.id) &&
                  <button onClick={() => {
                    updateNote(note.id, note.note)
                  }}><Icon icon="edit" fontSize="24px" className={'hover:text-green-500'}/></button>}
                {(note.user_id === user.id) && <button onClick={() => {
                  deleteNote(note.id)
                }}><Icon icon="delete" fontSize="24px" className={'hover:text-red-500'}/></button>}
              </div>
              {(noteId === note.id) ?
                <EditBox noteId={noteId} note={note.note} callback={() => {
                  mutate()
                  setNoteId(0)
                }} /> :
                <>{note.note}</>
              }
            </div>
            <div className={'w-2/5 border-l-2 border-primary-200 pl-4'}>
              <div className={'flex flex-row align-middle'}>
                <Avatar url={note.user?.profile_image} name={note.user?.name}/>
                <div className={'m-2'}>{note.user?.name}</div>
              </div>
              <div className={'ml-12 text-gray-500 text-sm'}>{convertToLocalDateTime(note.updated_at)}</div>
            </div>
          </div>
        </div>
      ))}
    </>
  )
}

export default NotesTable

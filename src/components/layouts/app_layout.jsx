import { Icon, Status } from '@bitcine/cinesend-theme'
import { ErrorBoundary } from '/src/components/error_boundary'
import Breadcrumbs from './breadcrumbs'
import SubNav from '/src/components/navigation/sub_nav'

const AppLayout = ({
  header, subheader, contentClassNames = '', background, breadcrumbs = [], buttons = [],
  pending = false, loading = false, children = null, tabs = []
}) =>
  <div className={`w-full h-full overflow-auto ml-4 md:ml-0 ${background ? background : ''}`}>
    {/* Page Container */}
    <div className={`pt-8 ${tabs.length > 0 ? 'bg-white' : ''}`}>
      {/* Breadcrumbs */}
      <div className="flex items-center space-x-4 mx-8">
        <Breadcrumbs breadcrumbs={breadcrumbs}/>
        {loading && <Icon icon="refresh" className="animate-spin-slow text-sm"/>}
      </div>
      {/* Page Heading */}
      <div className="flex items-center justify-between mt-2 mx-8">
        {header && <h3 className="font-normal text-gray-900 leading-tight">
          {header}
        </h3>}
        <div className="flex items-right space-x-4 ml-8">
          {buttons}
        </div>
      </div>
      {subheader && <div className="mt-2 text-gray-600  mx-8">
        {subheader}
      </div>
      }
      {tabs.length > 0 ? <SubNav tabs={tabs}/> : null}
    </div>

    {/* Page Content */}
    <Status pending={pending}>
      <div className={`flex flex-col ${contentClassNames}`}>
        <div className="bg-gray-50 flex-auto">
          {children ? <main>
            <ErrorBoundary>
              {children}
            </ErrorBoundary>
          </main> : null}
        </div>
      </div>
    </Status>
  </div>

export default AppLayout

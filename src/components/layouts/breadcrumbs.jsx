import { useNavigate } from 'react-router-dom'
import Icon from '@bitcine/cinesend-theme/dist/icon'
import { v4 as uuid } from 'uuid';

const Breadcrumbs = ({ breadcrumbs = [] }) => {
  const navigate = useNavigate()
  if (breadcrumbs.length === 0) {
    return null
  }
  return (
    <div className="flex items-center space-x-2">
      {breadcrumbs.map((breadcrumb, index) =>
        <div className="flex items-center space-x-2"
          key={uuid()}>
          <span
            className={`${breadcrumb.to ? 'cursor-pointer text-gray-500' : 'text-grey-900'}`}
            onClick={() => navigate(breadcrumb.to)}>
            {breadcrumb.text}
          </span>
          {index !== breadcrumbs.length - 1 ? <Icon icon="chevron_right" className="text-sm text-grey-400"/> : null}
        </div>
      )}
    </div>
  )
}

export default Breadcrumbs

import React, { useEffect, useState } from 'react'
import { useParams } from 'react-router-dom'
import { NetworkInterfaces, Status } from '@bitcine/cinesend-theme'
import { useQuery } from '@tanstack/react-query'

const ServerNetworking = () => {

  const { deviceID } = useParams()

  const apiUrl = `/api/media-managers/${deviceID}`

  const { data, isFetching } = useQuery({ queryKey: [apiUrl], refetchOnWindowFocus: false })


  const [errors, setErrors] = useState([])
  const [localDevice, setLocalDevice] = useState({})

  const updateState = newData => {
    setLocalDevice({ ...localDevice, ...newData })
  }

  useEffect(() => {
    setLocalDevice(data?.mediaManager)
  }, [data])

  return (
    <Status pending={isFetching} className={'md:flex-row md:space-x-2'}>
      <NetworkInterfaces networkInterfaces={data?.mediaManager.network_interfaces} isRefetching={isFetching}/>
      <div className={'p-2 p-4 md:space-x-4'}></div>
    </Status>
  )

}

export default ServerNetworking

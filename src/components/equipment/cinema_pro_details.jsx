import React, { useState } from 'react'
import ErrorMessages from '/src/components/error_messages'
import { useParams } from 'react-router-dom'
import { Status, Button } from '@bitcine/cinesend-theme'
import SelectCinemaSite from '/src/components/organizations/select_cinema_site'
import FIELDS from '/src/constants/sites/fields'
import FieldsForm from '/src/components/fields_form'
import { v4 as uuid } from 'uuid'
import { useSnackbar } from '/src/hooks/snackbar'
import axios from '/src/lib/axios'
import CinemaProContent from './cinema_pro_content'
import CommandLog from '/src/components/fazzt/command_log'
import Dcps from '/src/components/fazzt/dcps'
import Transfers from '/src/components/fazzt/transfers'
import { useQuery } from '@tanstack/react-query'

const CinemaProDetails = () => {
  let { addMessage } = useSnackbar()

  const { deviceID } = useParams()
  const apiUrl = `/api/cinema-pro-servers/${deviceID}`

  const { data, isFetching, error} = useQuery({ queryKey: [apiUrl] })
  let device = data?.cinema_pro

  const [value, setValue] = useState(device?.cinema_site_id)
  const [submitting, setSubmitting] = useState(false)

  return (
    <Status pending={isFetching}>
      <div className="flex flex-col space-y-4">
        <div className="grid grid-cols-6 gap-2 place-items-end">
          <SelectCinemaSite
            disabled={submitting}
            className={'col-span-5'}
            value={value}
            onChange={(value) => {
              setValue(value)
            }}
          />
          <Button
            className={'w-full col-span-1'}
            disabled={submitting}
            onClick={() => {
              setSubmitting(true)
              // post value to the endpoint
              axios.put(apiUrl, {
                cinema_site_id: value
              }).then(res => {
                addMessage('Site assignment updated.')
              }).catch(error => {
                addMessage('Whoops, something went wrong! Try again.', 'error')
              }).finally(() => {
                setSubmitting(false)
              })
            }}
          >Save</Button>
        </div>

        <div className="flex space-x-4">
          {device && FIELDS.cinema_pro.map((group, index) =>
            <FieldsForm
              key={uuid()}
              data={device}
              {...group}/>
          )}
        </div>

        <CinemaProContent cinemaProServer={device}/>
        <CommandLog />
        <Dcps/>
        <Transfers/>

      </div>
    </Status>
  )

}

export default CinemaProDetails

import { useEffect, useState } from 'react'
import { Table, Tag } from '@bitcine/cinesend-theme'
import { convertToLocal, convertToLocalDateTime } from '/src/helpers/convert_date'
import { useNavigate } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'

function LivestreamTable ({ apiUrl, updateTitle = (title) => {} }) {

  const navigate = useNavigate()
  const defaults = localStorage.getItem(apiUrl) !== null
    ? JSON.parse(localStorage.getItem(apiUrl))
    : { search: '', page: 0, take: 15, sortBy: 'created_at', sortDirection: 'asc' }

  const [queries, setQueries] = useState(defaults)
  const [checked, setChecked] = useState([])

  const params = new URLSearchParams(queries).toString()
  const url = `${apiUrl}?${params}`

  const setQuery = (data) => {
    const newQueries = { ...queries, ...data }
    // if a page wasn't clicked, and anything else was changed reset page to 0.
    if (data && !data.page) {
      newQueries.page = 0
    }
    setQueries(newQueries)
    localStorage.setItem(apiUrl, JSON.stringify(newQueries))
  }


  const { data: atvs, error } = useQuery({ queryKey: [url], refetchOnWindowFocus: false})

  useEffect(() => {
    if (checked.length > 0) {
      updateTitle(`Equipment (${checked.length})`)
    }
    else {
      updateTitle('Equipment')
    }
  }, [checked])

  return (
    <Table
      status={{
        pending: !atvs,
        pendingMessage: 'Loading Livestream Decoders list...',
        error: error
      }}
      header={{
        columns: [
          { text: 'Serial Number', key: 'serial_number' },
          { text: 'Physical Location' },
          { text: 'Status' },
          { text: 'Last Updated Date', key: 'status_updated_at' },
          { text: 'Created Date', key: 'created_at' }
        ],
        searching: {
          search: queries.search,
          searchPlaceholder: 'Search...',
          onSearch: search => setQuery({ search }),
          rightIcon: {
            icon: 'close',
            onClick: () => setQuery({search: ''}),
            tooltip: {
              text: "Clear search"
            }
          }
        },
        sorting: {
          options: [
            { key: 'serial_number', label: 'Serial Number' },
            { key: 'status_updated_at', label: 'Last Updated Date' },
            { key: 'created_at', label: 'Created Date' }
          ],
          key: queries.sortBy,
          sortingKey: queries.sortBy,
          direction: queries.sortDirection,
          onSortChange: (sort) => setQuery({ sortBy: sort.key, sortDirection: sort.direction })
        },
        // checkbox: {
        //   checked: checked.length > 0,
        //   indeterminate: atvs?.total && checked.length !== atvs?.total,
        //   onChange: () => {
        //     if (checked.length === 0) {
        //       setChecked(atvs?.data.map(({ id }) => id))
        //     }
        //     else {
        //       setChecked([])
        //     }
        //   }
        // }
      }}
      widths={[160, 200, 100, 90, 90]}
      body={{
        data: atvs?.data,
        row: {
          spaced: true,
          onClick: (event, data) => navigate(`/livestream-decoder/${data.id}`),
          // checkbox: {
          //   checked: (data, index) => checked.includes(data.id),
          //   onChange: (data, index) => (checked.includes(data.id)
          //     ? setChecked(checked.filter(i => i !== data.id))
          //     : setChecked([...checked, data.id]))
          // },
          render: [
            ({ serial_number, organization }) => <div className='flex flex-col'>
              <span>{serial_number}</span>
              {organization ? <span className='text-xs font-light'>{organization.name}</span> : null}
            </div>,
            ({cinema_site}) => cinema_site?.name ?? 'Not Assigned',
            ({ status }) => <Tag
              outline
              type={status === 'active' ? 'success' : 'primary'}
              label={<div className='capitalize'>{status}</div>}/>,
            ({ status_updated_at }) => convertToLocalDateTime(status_updated_at),
            ({ created_at }) => convertToLocal(created_at)
          ]
        },
        empty: {
          title: 'No Livestream Decoders found!',
          text: 'Refine your filters.',
          icon: 'dns'
        }
      }}
      paginate={{
        totalRows: atvs?.total,
        currentPage: atvs?.current_page - 1,
        rowsPerPage: atvs?.per_page,
        onPageChange: page => setQuery({ page }),
        onRowsPerPageChange: take => setQuery({ take })
      }}
    />
  )
}

export default LivestreamTable

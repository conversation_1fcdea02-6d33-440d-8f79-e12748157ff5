import React from 'react'
import ErrorMessages from '/src/components/error_messages'
import { useParams } from 'react-router-dom'
import { Status } from '@bitcine/cinesend-theme'
import SelectCinemaSite from '/src/components/organizations/select_cinema_site'
import { useQuery } from '@tanstack/react-query'

const LivestreamDetails = () => {

  const { deviceID } = useParams()
  const apiUrl = `/api/apple-televisions/${deviceID}`

  const { data, isFetching, error} = useQuery({ queryKey: [apiUrl] })
  let device = data?.appleTelevision

  return (
    <Status pending={isFetching}>
      <div className="flex flex-col space-y-4">
        <SelectCinemaSite
          value={device?.cinema_site_id}
        />

        <div className="flex space-x-4">
        </div>
        <ErrorMessages errors={error}/>
      </div>
    </Status>
  )

}

export default LivestreamDetails

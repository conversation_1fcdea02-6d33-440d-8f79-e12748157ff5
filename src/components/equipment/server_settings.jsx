import React, { useEffect, useState } from 'react'
import { useDevices } from '/src/hooks/devices'
import ErrorMessages from '/src/components/error_messages'
import FIELDS from '/src/constants/sites/fields'
import FieldsForm from '/src/components/fields_form'
import { useParams } from 'react-router-dom'
import { Select, Button, Status, SegmentedControl } from '@bitcine/cinesend-theme'
import SelectCinemaSite from '/src/components/organizations/select_cinema_site'
import NetworkInterfaces from '/src/components/hardware/interfaces'
import Ports from '/src/components/hardware/ports'
import SelectWarehouse from '/src/components/organizations/select_warehouse'
import NotesPanel from '/src/components/notes/panel'
import { useQuery } from '@tanstack/react-query'

const ServerSettings = () => {

  const { updateDevice } = useDevices()
  const { deviceID } = useParams()

  const apiUrl = `/api/media-managers/${deviceID}`

  const { data, isFetching } = useQuery({ queryKey: [apiUrl], refetchOnWindowFocus: false })

  const [errors, setErrors] = useState([])
  const [localDevice, setLocalDevice] = useState({})

  const updateState = newData => {
    setLocalDevice({ ...localDevice, ...newData })
  }
  const saveField = newData => {
    updateDevice({
      deviceID: localDevice.id,
      ...newData,
      setErrors: errors => setErrors(errors)
    })
  }

  useEffect(() => {
    setLocalDevice(data?.mediaManager)
  }, [data])

  return (
    <Status pending={isFetching}>
      <div className="flex flex-col space-y-4">
        Coming Soon.
      </div>
    </Status>
  )

}

export default ServerSettings

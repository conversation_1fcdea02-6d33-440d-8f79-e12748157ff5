import { useEffect, useState } from 'react'
import { Table, Tag } from '@bitcine/cinesend-theme'
import { convertToLocal, convertToLocalDateTime } from '/src/helpers/convert_date'
import { useNavigate } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'

function CinemaProTable ({ apiUrl, updateTitle = (title) => {} }) {

  const navigate = useNavigate()
  const defaults = localStorage.getItem(apiUrl) !== null
    ? JSON.parse(localStorage.getItem(apiUrl))
    : { search: '', page: 0, take: 15, sortBy: 'created_at', sortDirection: 'asc' }

  const [queries, setQueries] = useState(defaults)
  const [checked, setChecked] = useState([])

  const params = new URLSearchParams(queries).toString()
  const url = `${apiUrl}?${params}`

  const setQuery = (data) => {
    const newQueries = { ...queries, ...data }
    // if a page wasn't clicked, and anything else was changed reset page to 0.
    if (data && !data.page) {
      newQueries.page = 0
    }
    setQueries(newQueries)
    localStorage.setItem(apiUrl, JSON.stringify(newQueries))
  }

  const { data: cinema_pros, error } = useQuery({ queryKey: [url], refetchOnWindowFocus: false }) 

  useEffect(() => {
    if (checked.length > 0) {
      updateTitle(`Equipment (${checked.length})`)
    }
    else {
      updateTitle('Equipment')
    }
  }, [checked])

  return (
    <Table
      status={{
        pending: !cinema_pros,
        pendingMessage: 'Loading Cinema Pro Servers list...',
        error: error
      }}
      header={{
        columns: [
          { text: 'Serial Number', key: 'serial_number' },
          { text: 'Physical Location' },
          { text: 'Status' },
          { text: 'Last Updated Date', key: 'status_updated_at' }
        ],
        searching: {
          search: queries.search,
          searchPlaceholder: 'Search...',
          onSearch: search => setQuery({ search }),
          rightIcon: {
            icon: 'close',
            onClick: () => setQuery({search: ''}),
            tooltip: {
              text: "Clear search"
            }
          }
        },
        sorting: {
          options: [
            { key: 'serial_number', label: 'Serial Number' },
            { key: 'status_updated_at', label: 'Last Updated Date' },
            { key: 'created_at', label: 'Created Date' }
          ],
          key: queries.sortBy,
          sortingKey: queries.sortBy,
          direction: queries.sortDirection,
          onSortChange: (sort) => setQuery({ sortBy: sort.key, sortDirection: sort.direction })
        },
        // checkbox: {
        //   checked: checked.length > 0,
        //   indeterminate: cinema_pros?.total && checked.length !== cinema_pros?.total,
        //   onChange: () => {
        //     if (checked.length === 0) {
        //       setChecked(cinema_pros?.data.map(({ id }) => id))
        //     }
        //     else {
        //       setChecked([])
        //     }
        //   }
        // }
      }}
      widths={[160, 200, 100, 90]}
      body={{
        data: cinema_pros?.data,
        row: {
          spaced: true,
          onClick: (event, data) => navigate(`/cinema-pros/${data.id}`),
          // checkbox: {
          //   checked: (data, index) => checked.includes(data.id),
          //   onChange: (data, index) => (checked.includes(data.id)
          //     ? setChecked(checked.filter(i => i !== data.id))
          //     : setChecked([...checked, data.id]))
          // },
          render: [
            ({ serial_number, receive_site_name }) => <div className="flex flex-col">
              <span>{serial_number}</span>
              {receive_site_name ? <span className="text-xs font-light">{receive_site_name}</span> : null}
            </div>,
            ({ cinema_site }) => cinema_site?.name ?? 'Not Assigned',
            ({ status }) => <Tag
              outline
              type={status === 'active' ? 'success' : 'primary'}
              label={<div className="capitalize">{status}</div>}/>,
            ({ updated_at }) => convertToLocalDateTime(updated_at)
          ]
        },
        empty: {
          title: 'No Cinema Pro Servers found!',
          text: 'Please refine your filters.',
          icon: 'dns'
        }
      }}
      paginate={{
        totalRows: cinema_pros?.total,
        currentPage: cinema_pros?.current_page - 1,
        rowsPerPage: cinema_pros?.per_page,
        onPageChange: page => setQuery({ page }),
        onRowsPerPageChange: take => setQuery({ take })
      }}
    />
  )
}

export default CinemaProTable

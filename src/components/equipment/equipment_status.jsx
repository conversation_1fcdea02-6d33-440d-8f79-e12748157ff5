import React from 'react'
import { convertToLocalDateTime } from '/src/helpers/convert_date'
import { Select } from '@bitcine/cinesend-theme'

const EquipmentStatus = ({ updated, status }) => {

  const statuses = [
    { label: 'Offline', value: false },
    { label: 'Online', value: true }
  ]

  return (
    <>
      {updated &&
        <div className="flex flex-col space-x-4 items-end">
          <Select
            className={'w-4/5'}
            disabled={true}
            options={statuses}
            value={statuses.find(s => s.value === status)}
            onChange={value => {}}
          />
          <small className={'mt-2 text-xs text-gray-400'}>
            Last online: {convertToLocalDateTime(updated)}</small>
        </div>
      }
    </>
  )
}

export default EquipmentStatus

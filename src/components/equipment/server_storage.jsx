import React, { useEffect, useState } from 'react'
import { useDevices } from '/src/hooks/devices'
import { useParams } from 'react-router-dom'
import { RaidHealth, Select, Button, Status, SegmentedControl } from '@bitcine/cinesend-theme'
import { useQuery } from '@tanstack/react-query'

const ServerStorage = () => {

  const { updateDevice } = useDevices()
  const { deviceID } = useParams()

  const apiUrl = `/api/media-managers/${deviceID}`

  const { data, isFetching } = useQuery({ queryKey: [apiUrl], refetchOnWindowFocus: false })


  const [errors, setErrors] = useState([])
  const [localDevice, setLocalDevice] = useState({})

  const updateState = newData => {
    setLocalDevice({ ...localDevice, ...newData })
  }
  const saveField = newData => {
    updateDevice({
      deviceID: localDevice.id,
      ...newData,
      setErrors: errors => setErrors(errors)
    })
  }

  useEffect(() => {
    setLocalDevice(data?.mediaManager)
  }, [data])

  return (
    <Status pending={isFetching}>
      <RaidHealth raid={data?.mediaManager.raid_status}/>
    </Status>
  )

}

export default ServerStorage

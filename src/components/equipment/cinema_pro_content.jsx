import humanFileSize from '/src/helpers/human_file_size'
import React from 'react'
import { Button, Icon } from '@bitcine/cinesend-theme'
import Card from '/src/components/layouts/card'
import { v4 as uuid } from 'uuid'

const CinemaProContent = ({ cinemaProServer }) => {

  let content = cinemaProServer?.content

  const statuses = {
    'PUBLISH' : 'Published',
    'UNPUBLISH' : 'Not Published'
  }

  const statusIcon = (status) => (
    <Button size={'small'}
      onClick={() => {
        console.log(status)
      }}
    >
      <Icon key={uuid()}
        icon={status === 'PUBLISH' ? 'unpublished' : 'publish'}
        className={'text-2xs'}
      />
    </Button>
  )

  const titleElement = (content, library) => <div key={uuid()}>
    {library ?
      <div key={uuid()} className={'flex flex-row justify-between'}>
        <div className={'text-gray-500'}>Asset ID: {content?.asset_uuid}
          {content.version ?
            <div>{content.version?.version_name} - {humanFileSize(content.version?.size)}</div>
            : <div>Content not found DCDC Library</div>}
        </div>
        <div>{statuses[content.library_status]} {statusIcon(content.library_status)}</div>
      </div>
      : (content.cru_status === 'PUBLISH' ? titleElement(content, true) : <></>)
    }
  </div>

  return (

    <Card title={'Content'} layout={'grid grid-cols-2 gap-2'}>
      <div className={'colspan-1 card-box'}>
        <h6 className={'border-b-2 border-primary-700 mb-2'}>
          Library
        </h6>
        {content && content.map(content => (titleElement(content, true)))}
      </div>
      <div className={'colspan-1 card-box'}>
        <h6 className={'border-b-2 border-primary-700 mb-2'}>
          CRU Drive
        </h6>
        {content && content.map(content => (titleElement(content, false)))}
      </div>
    </Card>

  )
}
export default CinemaProContent

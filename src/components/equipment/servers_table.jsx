import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import { Table, Tag } from '@bitcine/cinesend-theme'
import { convertToLocal, convertToLocalDateTime } from '/src/helpers/convert_date'
import { useNavigate } from 'react-router-dom'
import ResetTableFilters from '/src/components/reset_table_filters'
import { useQuery } from '@tanstack/react-query'

function ServersTable ({ apiUrl, updateTitle = (title) => {}}) {

  const navigate = useNavigate()

  const headerDefaults = { search: '', page: 0, take: 15, sortBy: 'created_at', sortDirection: 'asc' }
  const defaults = localStorage.getItem(apiUrl) !== null
    ? JSON.parse(localStorage.getItem(apiUrl))
    : headerDefaults

  const [queries, setQueries] = useState(defaults)
  const [checked, setChecked] = useState([])
  const [filter, setFilter] = useState({})

  const filterOptions = [
    {
      label: 'Status',
      key: 'status',
      type: 'radio_buttons',
      options: [
        { key: 'warehouse', label: 'Warehoused' },
        { key: 'deployed', label: 'Deployed' }
      ]
    }
  ]

  const params = new URLSearchParams(queries).toString()
  const url = `${apiUrl}?${params}`

  const setQuery = (data, isFilter = false, isReset = false) => {
    const newQueries = { ...queries, ...data }
    if (isFilter && data === null || isReset) {
      delete newQueries.status
      setFilter({})
    }
    // if a page wasn't clicked, and anything else was changed reset page to 0.
    if (data && !data.page) {
      newQueries.page = 0
    }
    setQueries(newQueries)
    localStorage.setItem(apiUrl, JSON.stringify(newQueries))
  }

  const { data: servers, error } = useQuery({ queryKey: [url] })

  useEffect(() => {
    if (checked.length > 0) {
      updateTitle(`Equipment (${checked.length})`)
    }
    else {
      updateTitle('Equipment')
    }
  }, [checked])

  return (
    <Table
      status={{
        pending: !servers,
        pendingMessage: 'Loading servers...',
        error: error
      }}
      header={{
        customElement: <ResetTableFilters filterCallback={() => {
          setQuery(headerDefaults, false, true);
        }}
        />,
        columns: [
          { text: 'Primary', key: 'is_primary' },
          { text: 'Serial Number', key: 'serial_number' },
          { text: 'Physical Location' },
          { text: 'Status' },
          { text: 'Online' },
          { text: 'Last Updated', key: 'status_updated_at' }
        ],
        filtering: {
          options: filterOptions,
          filters: filter,
          onFiltersChange: value => {
            setFilter(value)
            setQuery(value, true)
          }
        },
        searching: {
          search: queries.search,
          searchPlaceholder: 'Search...',
          onSearch: search => setQuery({ search }),
          rightIcon: {
            icon: 'close',
            onClick: () => setQuery({search: ''}),
            tooltip: {
              text: "Clear search"
            }
          }
        },
        sorting: {
          options: [
            { key: 'serial_number', label: 'Serial Number' },
            { key: 'status_updated_at', label: 'Last Updated' }
          ],
          key: queries.sortBy,
          sortingKey: queries.sortBy,
          direction: queries.sortDirection,
          onSortChange: (sort) => setQuery({ sortBy: sort.key, sortDirection: sort.direction })
        },
        // checkbox: {
        //   checked: checked.length > 0,
        //   indeterminate: servers?.total && checked.length !== servers?.total,
        //   onChange: () => {
        //     if (checked.length === 0) {
        //       setChecked(servers?.data.map(({ id }) => id))
        //     }
        //     else {
        //       setChecked([])
        //     }
        //   }
        // }
      }}
      widths={[80, 200, 160, 90, 90, 'auto']}
      body={{
        data: servers?.data,
        row: {
          spaced: true,
          onClick: (event, data) => navigate(`/server/${data.id}`),
          // checkbox: {
          //   checked: (data, index) => checked.includes(data.id),
          //   onChange: (data, index) => (checked.includes(data.id)
          //     ? setChecked(checked.filter(i => i !== data.id))
          //     : setChecked([...checked, data.id]))
          // },
          render: [
            ({ is_primary }) => (is_primary ? 'Yes' : 'No'),
            ({ serial_number, name }) => <div className="flex flex-col">
              <div>{serial_number}</div>
            </div>,
            ({ cinema_site }) => cinema_site?.name,
            ({ cinema_site }) => ((cinema_site?.circuit === 'warehouse') ?
              <Tag
                outline
                type={'primary'}
                label={<div className="capitalize">Warehoused</div>}/> :
              <Tag
                outline
                type={'success'}
                label={<div className="capitalize">Deployed</div>}/>
            ),
            ({ status_updated_at }) => (dayjs(status_updated_at).isBefore(
              dayjs().subtract(1, 'hour')) ? 'Offline' : 'Online'),
            ({ status_updated_at }) => convertToLocalDateTime(status_updated_at)
          ]
        },
        empty: {
          title: 'No servers found!',
          text: 'Refine your filters.',
          icon: 'dns'
        }
      }}
      paginate={{
        totalRows: servers?.total,
        currentPage: servers?.current_page - 1,
        rowsPerPage: servers?.per_page,
        onPageChange: page => setQuery({ page }),
        onRowsPerPageChange: take => setQuery({ take })
      }}
    />
  )
}

export default ServersTable

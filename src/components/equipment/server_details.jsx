import React, { useEffect, useState } from 'react'
import { useDevices } from '/src/hooks/devices'
import ErrorMessages from '/src/components/error_messages'
import FIELDS from '/src/constants/sites/fields'
import FieldsForm from '/src/components/fields_form'
import { useParams } from 'react-router-dom'
import { Select, Status } from '@bitcine/cinesend-theme'
import SelectCinemaSite from '/src/components/organizations/select_cinema_site'
import SelectWarehouse from '/src/components/organizations/select_warehouse'
import NotesPanel from '/src/components/notes/panel'
import { useAuth } from '/src/hooks/auth'
import { useQuery } from '@tanstack/react-query'

const ServerDetails = () => {

  const { updateDevice } = useDevices()
  const { deviceID } = useParams()
  const { checkPermission } = useAuth()
  const canUpdate = checkPermission('update-equipment')

  const apiUrl = `/api/media-managers/${deviceID}`


  const { data, isFetching } = useQuery({ queryKey: [apiUrl], refetchOnWindowFocus: false })

  const [errors, setErrors] = useState([])
  const [localDevice, setLocalDevice] = useState({})
  const [activeTab, setActiveTab] = useState(true)

  const locationOptions = [
    {
      label: 'Warehoused',
      value: true
    },
    {
      label: 'Deployed',
      value: false
    }
  ]

  const updateState = newData => {
    setLocalDevice({ ...localDevice, ...newData })
  }
  const saveField = newData => {
    updateDevice({
      deviceID: localDevice.id,
      ...newData,
      setErrors: errors => setErrors(errors)
    })
  }

  useEffect(() => {
    setLocalDevice(data?.mediaManager)
    setActiveTab(data?.mediaManager.is_warehoused)
  }, [data])

  return (
    <Status pending={isFetching}>
      <div className="flex flex-col space-y-4">
        <Select
          label={'Hardware Location'}
          className={'w-1/2'}
          size="small"
          options={locationOptions}
          disabled={!canUpdate}
          value={locationOptions.find(location => location.value === activeTab)}
          onChange={(e) => {
            setActiveTab(e.value)
          }}
        />

        {(activeTab === false) ?
          <div className={'w-1/2'}>
            <SelectCinemaSite
              value={localDevice?.cinema_site_id}
              disabled={!canUpdate}
              onChange={(value) => {
                const newData = { cinema_site_id: value }
                updateState(newData)
                saveField(newData)
              }}/>
          </div> :
          <div className={'w-1/2'}>
            <SelectWarehouse
              label={'Warehoused Location'}
              value={localDevice?.cinema_site_id}
              onChange={(value) => {
                const newData = { cinema_site_id: value }
                updateState(newData)
                saveField(newData)
              }}
            /></div>}
        <div className="flex space-x-4">
          {localDevice && FIELDS.device.map((group, index) =>
            <FieldsForm
              disabled={!canUpdate}
              key={index}
              data={localDevice}
              updateState={updateState}
              saveField={saveField}
              {...group}/>
          )}
        </div>

        <ErrorMessages errors={errors}/>

        <NotesPanel modelId={deviceID} modelName={'equipment'}/>
      </div>
    </Status>
  )

}

export default ServerDetails

import { v4 as uuid } from 'uuid';
import { convertToLocalDateTime } from '../../helpers/convert_date'
function AppleTVs ({site}) {

  return (
    <>
      <div className="text-xl font-bold mt-8 mb-4">Livestream Decoders</div>
      {site.apple_televisions.map(at =>
        <div className='flex flex-row  mb-2 py-4  card-box justify-between' key={uuid()}>
          <div className="w-1/4 truncate">{at.serial_number}</div>
          <div className="w-1/4 truncate">{at.remote_ip_address}</div>
          <div className="w-1/4">Latest ping: <br/>{convertToLocalDateTime(at.status_updated_at)}</div>
        </div>

      )}
    </>

  )
}

export default AppleTVs

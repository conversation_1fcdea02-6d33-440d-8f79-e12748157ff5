import ReactJson from 'react-json-view'
import { v4 as uuid } from 'uuid'
import InteraceRow from './interace_row'

function NetworkInterfaces ({ mediaManager }) {

  const ipAddresses = mediaManager?.ip_addresses ?? []
  const interfaces = mediaManager?.network_interfaces ?? []

  return (
    <div className="">
      <h5 className="text-xl font-bold">Networking for {mediaManager.name}</h5>
      <h6 className={'my-4'}>Public IP</h6>
      <p className={'mx-4'}>{mediaManager.public_ip ?? 'N/A'}</p>
      <h6 className={'my-4'}>WAN Interface</h6>
      <p className={'mx-4'}>{mediaManager.wan_dhcp ? 'DHCP Enabled' : 'Static'}</p>
      <p className={'mx-4'}>{mediaManager.wan_ip?.join(', ') ?? 'N/A'}</p>
      <h6 className={'my-4'}>LAN Interface</h6>
      <p className={'mx-4'}>{mediaManager.lan_dhcp ? 'DHCP Enabled' : 'Static'}</p>
      <p className={'mx-4'}>{mediaManager.lan_ip?.join(', ') ?? 'N/A'} </p>
    </div>
  )
}

export default NetworkInterfaces

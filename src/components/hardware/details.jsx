import React, { useState } from 'react'
import { useDevices } from '/src/hooks/devices'
import ErrorMessages from '/src/components/error_messages'
import FIELDS from '/src/constants/sites/fields'
import FieldsForm from '/src/components/fields_form'

const Details = ({ device }) => {
  const [errors, setErrors] = useState([])
  const [data, setData] = useState(device)
  const updateState = newData => {
    setData({ ...data, ...newData })
  }
  const { updateDevice } = useDevices()
  const saveField = newData => {
    updateDevice({
      deviceID: device.id,
      ...newData,
      setErrors: errors => setErrors(errors)
    })
  }
  return (
    <div className='flex flex-col space-y-4'>
      <div className='flex space-x-4'>
        {FIELDS.device.map((group, index) =>
          <FieldsForm
            key={index}
            data={data}
            updateState={updateState}
            saveField={saveField}
            {...group}/>
        )}
      </div>
      <ErrorMessages errors={errors}/>
    </div>
  )

}

export default Details

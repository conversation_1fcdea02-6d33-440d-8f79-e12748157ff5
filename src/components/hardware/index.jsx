import dayjs from 'dayjs'
import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Status, Table, ButtonDropdown } from '@bitcine/cinesend-theme'
import { convertToLocal } from '../../helpers/convert_date'
import { useDevices } from '../../hooks/devices'
import AppleTVs from './appletvs'
import NetworkInterfaces from './interfaces'
import MediaManagers from './mediamanagers'
import Ports from './ports'
import Slide from '/src/components/slide'

const Hardware = ({ site }) => {

  const [activeMediaManager, setActiveMediaManager] = useState(null)
  const [showDevice, setShowDevice] = useState(false)

  const navigate = useNavigate()
  const { updateDevice } = useDevices()
  const saveField = newData => {
    updateDevice({
      deviceID: newData.id,
      ...newData,
      setErrors: () => {}
    })
  }

  // merge site mediaManagers and site apple_televisions together one data set for this silly table.
  const livestreams = site.apple_televisions
  const cpros = site.cinema_pro_servers
  const mapped = livestreams.map(obj => ({ ...obj, type: 'Decoder', is_primary: true }))
  const mapped2 = cpros.map(obj => ({ ...obj, type: 'CinemaPro', is_primary: true }))
  const devices = site.media_managers.concat(mapped, mapped2)

  return (
    <Status pending={!site}>

      <Table
        status={{
          pending: !devices,
          pendingMessage: 'Loading Equipment...'
        }}
        header={{
          columns: [
            { text: 'Status' },
            { text: 'Primary', key: 'is_primary' },
            { text: 'Type' },
            { text: 'Serial Number' },
            { text: 'Last Heartbeat' },
            { text: 'Public IP' },
            { text: 'WAN IP' },
            { text: 'LAN IP' },
            { text: '' }
          ]
        }}
        widths={[80, 80, 120, 300, 140, 160, 160, 160, 'auto']}
        body={{
          data: devices ?? [],
          row: {
            onClick: (event, data) => {
              if (data.type === 'Decoder') {
                navigate(`/livestream-decoder/${data.id}`)
              }
              else if (data.type === 'CinemaPro') {
                navigate(`/cinema-pros/${data.id}/asset`)
              }
              else {
                navigate(`/server/${data.id}`)
              }
            },
            spaced: true,
            render: [
              ({ status_updated_at }) => (dayjs(status_updated_at).isBefore(
                dayjs().subtract(2, 'hour')) ? 'Offline' : 'Online'),
              ({ is_primary }) => (is_primary ? 'Yes' : 'No'),
              ({ type }) => type ?? 'CS1',
              ({ serial_number }) => serial_number,
              ({ status_updated_at }) => convertToLocal(status_updated_at),
              ({ public_ip }) => public_ip ?? 'N/A',
              ({ wan_ip, wan_dhcp }) => (wan_ip ?
                (wan_ip[0] ? wan_ip[0] : 'N/A') : 'N/A') + (wan_dhcp ? ' (dhcp)' : ' (static)'),
              ({ lan_ip, lan_dhcp }) => (lan_ip ?
                (lan_ip[0] ? lan_ip[0] : 'N/A') : 'N/A') + (lan_dhcp ? ' (dhcp)' : ' (static)'),
              data =>
                <>
                  {!data.is_primary && data.type !== 'Decoder' &&
                  <ButtonDropdown
                    button={{
                      minimal: true, icon: 'more_vert', type: 'neutral'
                    }}
                    dropdown={{
                      content: [
                        {
                          text: 'Make Primary',
                          icon: 'upgrade',
                          onClick: () => {
                            if (window.confirm('setting this device to primary will remove any others')) {
                              const newData = { id: data.id, is_primary: 1 }
                              saveField(newData)
                            }
                          },
                          show: !data.is_primary && data.type !== 'Decoder'
                        }
                      ].filter(opt => opt.show)
                    }}/>
                  }
                </>
            ]
          },
          empty: {
            title: 'No Equipment found!',
            text: '',
            icon: 'movie'
          }
        }}
      />

      {showDevice &&
        <Slide header="Details" onClose={() => {
          setShowDevice(false)
          setActiveMediaManager({})
        }}>
          <NetworkInterfaces mediaManager={activeMediaManager}/>
          <Ports mediaManager={activeMediaManager} />
        </Slide>
      }
    </Status>
  )
}

export default Hardware

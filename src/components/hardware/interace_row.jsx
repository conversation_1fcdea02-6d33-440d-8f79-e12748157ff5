import { v4 as uuid } from 'uuid';

const showFields = ['mac', 'address', 'netmask']

const InteraceRow = ({ row }) => (
  <div className="flex flex-row">
    {showFields.map((val, index) =>
      <div className="w-1/3" key={uuid()}>
        <div className="font-bold">{val}</div>
        <input
          className="p-1"
          key={index}
          disabled={true}
          value={row[val]}
        />
      </div>)}
  </div>
)

export default InteraceRow

import { convertToLocalDateTime } from '/src/helpers/convert_date'
import dayjs from 'dayjs'
import { v4 as uuid } from 'uuid'
import { Button } from '@bitcine/cinesend-theme'
import { useDevices } from '/src/hooks/devices'

function MediaManagers ({ site, activeMediaManager, onClick, mutate }) {

  const { updateDevice } = useDevices()
  const saveField = newData => {
    updateDevice({
      deviceID: newData.id,
      ...newData,
      setErrors: () => {}
    })
  }

  return (
    <>
      <div className="text-xl font-bold mb-4">Catch Servers</div>

      {site.media_managers.map(mm =>
        <div
          className={`flex w-full flex-row mb-2 p-4 bg-white border border-[#EDEDFB] justify-between 
          rounded-lg drop-shadow-[0_4px_6px_rgba(0,0,0,0.15)] cursor-pointer ${activeMediaManager?.serial_number ===
          mm.serial_number
            ? 'bg-primary-300 text-white'
            : ''}`}
          key={uuid()} onClick={() => {onClick(mm)}}>
          <div className="w-1/4 truncate">{mm.is_primary ?
            'Primary' :
            <Button secondary={true}
              onClick={() => {
                if (window.confirm('setting this device to primary will remove any others')) {
                  const newData = { id: mm.id, is_primary: 1 }
                  saveField(newData)
                }
              }}
              disabled={(dayjs(mm.status_updated_at).isBefore(
                dayjs().subtract(1, 'hour')))}>Set Primary</Button>}
          </div>
          <div className="w-1/4 truncate">{mm.serial_number}</div>
          <div className="w-1/4">{mm.public_ip ?? 'N/A'}</div>
          <div className="w-1/4">{(dayjs(mm.status_updated_at).isBefore(
            dayjs().subtract(1, 'hour')) ? 'Offline' : 'Online')}, Last ping: <br/>{convertToLocalDateTime(
            mm.status_updated_at)}</div>
        </div>
      )}
    </>
  )
}

export default MediaManagers

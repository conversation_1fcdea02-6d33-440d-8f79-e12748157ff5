import { convertToLocal } from '/src/helpers/convert_date'
import { v4 as uuid } from 'uuid'

const TableCard = ({ name, headers, values, colorCode, className, onRowClick }) =>
  <div className="w-full h-full hover:shadow bg-white border
      border-[#EDEDFB] rounded-lg drop-shadow-[0_4px_6px_rgba(0,0,0,0.15)] p-6">
    <table className="table-fixed w-full h-full border-collapse">
      <thead>
        <tr>
          {headers.map(header => (header.isVisible ?
            <th key={uuid()} className={`border-b-2 text-sm font-subheader font-light ${header.width ?? ''}
              text-left`}>{header.label}</th> : ''))}
        </tr>
      </thead>
      <tbody>
        {values.length === 0 && <tr>
          <td>No data for now.</td>
        </tr>}
        {values.map(row =>
          <tr key={uuid()} className={`hover:bg-gray-200 ${typeof onRowClick === 'function' ? 'cursor-pointer' : ''}`}
            onClick={() => {
              if (typeof onRowClick === 'function') {
                onRowClick(row)
              }
            }}>
            {headers.map(header => (header.isVisible ?
              <td key={uuid()} className={`py-4 px-2 ${header.width ?? ''}`}>{header.processor
                ? header.processor(row[header.key], row)
                : row[header.key]}</td>
              : ''))}
          </tr>
        )}
      </tbody>
    </table>
  </div>

export default TableCard

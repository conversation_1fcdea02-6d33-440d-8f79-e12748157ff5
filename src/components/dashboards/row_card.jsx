const RowCard = ({name, value, colorCode, className, onClick}) =>
  <div className={`w-full sm:w-1/2 lg:w-1/4 mb-2 px-1 ${className}`} onClick={onClick}>
    <div className="h-full hover:shadow bg-white border
      border-[#EDEDFB] rounded-lg drop-shadow-[0_4px_6px_rgba(0,0,0,0.15)] p-6">
      <div className="text-gray-700">
        <div className={`font-subheader font-medium text-[1.4rem] ${colorCode}`}>{value}</div>
        <div className="text-sm">{name}</div>
      </div>
    </div>
  </div>

export default RowCard

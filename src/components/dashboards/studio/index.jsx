import AppLayout from '/src/components/layouts/app_layout'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '/src/hooks/auth'
import DashboardRow from '/src/components/dashboards/dashboard_row'
import RowCard from '/src/components/dashboards/row_card'
import { Icon } from '@bitcine/cinesend-theme'
import { useQuery } from '@tanstack/react-query'

function StudioDashboard () {
  let navigate = useNavigate()
  const { user } = useAuth()

  const { data, error } = useQuery({ queryKey: ['/api/dashboard/studio'] })
  if (error) {
    if (error.response.status === 409) {
      throw error
    }
    navigate('/verify-email')
  }

  const destinations = {
    bookings: { url: '/bookings', storage: '/api/bookings' }
  }

  const setAndGo = (destination, filters) => {
    // set the local storage for the predefined filter and go to that
    // primary list.
    let url = destinations[destination].url
    let storage = destinations[destination].storage
    localStorage.setItem(storage, JSON.stringify(filters))
    navigate(url)

  }

  const loadingIcon = <Icon icon="refresh" className="animate-spin-slow"/>
  return (
    <AppLayout
      contentClassNames={'p-8'}
      header={'Dashboard'}
      subheader={`Welcome back, ${user.name}!`}>
      <div className="flex flex-col w-full max-w-4xl">
        <DashboardRow
          rowTitle="Bookings (last 30 days)">
          <RowCard name="Booked"
            className="cursor-pointer"
            onClick={() => setAndGo('bookings', { status: 'booked' })}
            value={data?.bookings?.pending ?? loadingIcon} colorCode="text-blue-600"/>
          <RowCard name="In Progress"
            className="cursor-pointer"
            onClick={() => setAndGo('bookings', { status: 'in_progress' })}
            value={data?.bookings?.transmitting ?? loadingIcon} colorCode="text-yellow-600"/>
          <RowCard name="Completed"
            className="cursor-pointer"
            onClick={() => setAndGo('bookings', { status: 'completed' })}
            value={data?.bookings?.completed ?? loadingIcon} colorCode="text-green-600"/>
          <RowCard name="Issues"
            className="cursor-pointer"
            onClick={() => setAndGo('bookings', { status: 'issues' })}
            value={data?.bookings?.error ?? loadingIcon} colorCode="text-red-600"/>
        </DashboardRow>
      </div>

    </AppLayout>
  )
}

export default StudioDashboard

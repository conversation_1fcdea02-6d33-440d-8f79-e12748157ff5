import AppLayout from '/src/components/layouts/app_layout'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '/src/hooks/auth'
import { useQuery } from '@tanstack/react-query'

function VendorDashboard () {
  let navigate = useNavigate()
  const { user } = useAuth()
  const { data, error } = useQuery({ queryKey: ['/api/dashboard'] })
  if (error) {
    if (error.response.status === 409) {
      throw error
    }
    navigate('/verify-email')
  }

  return (
    <AppLayout
      contentClassNames={'p-8'}
      header={'Dashboard'}>
      <>Welcome back, {user.name}</>
    </AppLayout>
  )
}

export default VendorDashboard

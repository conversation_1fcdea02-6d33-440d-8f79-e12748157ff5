import AppLayout from '/src/components/layouts/app_layout'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '/src/hooks/auth'
import DashboardRow from '/src/components/dashboards/dashboard_row'
import RowCard from '/src/components/dashboards/row_card'
import { convertToLocalDateTime } from '/src/helpers/convert_date'
import { Icon } from '@bitcine/cinesend-theme'
import { useQuery } from '@tanstack/react-query'

function AdminDashboard () {

  const { user } = useAuth()
  const { data, isLoading } = useQuery({ queryKey: ['/api/dashboard'], refetchInterval: 15000 })
  const navigate = useNavigate()

  const destinations = {
    sites: { url: '/sites', storage: '/api/cinemas' },
    bookings: { url: '/bookings', storage: '/api/bookings' },
    transfers: { url: '/transfers', storage: '/api/transfers' }
  }

  const setAndGo = (destination, filters) => {
    // set the local storage for the predefined filter and go to that
    // primary list.
    let url = destinations[destination].url
    let storage = destinations[destination].storage
    localStorage.setItem(storage, JSON.stringify(filters))
    navigate(url)

  }
  const loadingIcon = <Icon icon="refresh" className="animate-spin-slow"/>
  return (
    <AppLayout
      contentClassNames={'p-8'}
      header={'Dashboard'}
      subheader={`Welcome back, ${user.name}!`}
    >
      <div className="flex flex-col w-full max-w-4xl">

        <DashboardRow rowTitle="Sites Status">
          <RowCard className="cursor-pointer"
            onClick={() => setAndGo('sites', { status: 'online' })}
            name="Online" value={data?.sites?.online ?? loadingIcon} colorCode="text-green-600"/>
          <RowCard className="cursor-pointer" name="Pending Install"
            onClick={() => setAndGo('sites', { status: 'pending' })}
            value={data?.sites?.pending ?? loadingIcon} colorCode="text-yellow-600"/>
          <RowCard name="Requires Attention" className="cursor-pointer"
            onClick={() => setAndGo('sites', { status: 'fault' })}
            value={data?.sites?.fault ?? loadingIcon} colorCode="text-red-600"/>
        </DashboardRow>

        <DashboardRow
          rowTitle="Bookings (last 30 days)">
          <RowCard name="Booked"
            className="cursor-pointer"
            onClick={() => setAndGo('bookings', { status: 'booked' })}
            value={data?.bookings?.pending ?? loadingIcon} colorCode="text-blue-600"/>
          <RowCard name="In Progress"
            className="cursor-pointer"
            onClick={() => setAndGo('bookings', { status: 'in_progress' })}
            value={data?.bookings?.transmitting ?? loadingIcon} colorCode="text-yellow-600"/>
          <RowCard name="Completed"
            className="cursor-pointer"
            onClick={() => setAndGo('bookings', { status: 'completed' })}
            value={data?.bookings?.completed ?? loadingIcon} colorCode="text-green-600"/>
          <RowCard name="Issues"
            className="cursor-pointer"
            onClick={() => setAndGo('bookings', { status: 'issues' })}
            value={data?.bookings?.error ?? loadingIcon} colorCode="text-red-600"/>
        </DashboardRow>

        <DashboardRow rowTitle="Transfers">
          <RowCard name="Queued"
            className="cursor-pointer"
            onClick={() => setAndGo('transfers', { status: 'pending' })}
            value={data?.deliveries?.queued ?? loadingIcon} colorCode="text-yellow-600"/>
          <RowCard name="In Progress"
            className="cursor-pointer"
            onClick={() => setAndGo('transfers', { status: 'transmitting' })}
            value={data?.deliveries?.transmitting ?? loadingIcon} colorCode="text-green-600"/>
          <RowCard name="Completed"
            className="cursor-pointer"
            onClick={() => setAndGo('transfers', { status: 'completed' })}
            value={data?.deliveries?.completed ?? loadingIcon} colorCode="text-green-600"/>
          <RowCard name="Requires Attention"
            className="cursor-pointer"
            onClick={() => setAndGo('transfers', { status: 'error' })}
            value={data?.deliveries?.fault ?? loadingIcon} colorCode="text-red-600"/>
        </DashboardRow>

        <small className="mt-4 text-gray-400">Data refreshes automatically.
        Last updated {convertToLocalDateTime(data?.last_update)}
        {(isLoading) &&
          <Icon icon="refresh" className="animate-spin-slow text-sm"/>
        }</small>
      </div>
    </AppLayout>
  )
}

export default AdminDashboard

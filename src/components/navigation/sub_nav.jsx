import { useLocation, useNavigate } from 'react-router-dom'
import { v4 as uuid } from 'uuid'

const SubNav = ({ tabs }) => {
  const navigate = useNavigate()
  const pathname = useLocation().pathname
  return (
    <div className="flex items-center border-b-2 mt-2 space-x-6">
      {tabs.map((tab, index) => {
        const isActive = pathname === tab.to
        const isVisible = tab.visible ?? true
        return (
          <div key={uuid()}
            onClick={() => navigate(tab.to)}
            className={`first:ml-8 capitalize font-medium py-4 border-b-4 
              cursor-pointer text-base font-subheader leading-4
            ${isActive ? 'text-primary-500 border-primary-500' : 'border-transparent text-primary-800'}
            ${isVisible ? 'inline-block' : 'hidden'}`}>
            <span className="h-6">{tab.name.replace('-', ' ')}</span>
          </div>
        )
      })}
    </div>
  )
}

export default SubNav

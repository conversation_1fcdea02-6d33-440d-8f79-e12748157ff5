import { useAuth } from '/src/hooks/auth'
import { useLocation, useNavigate } from 'react-router-dom'
import { Sidebar } from '@bitcine/cinesend-theme'
import FullLogo from '/src/components/logos/full_logo'
import SmallLogo from '../logos/small_logo'

const Navigation = () => {
  const { user, logout, checkPermission, checkRole } = useAuth()
  const navigate = useNavigate()
  const pathname = useLocation().pathname

  const dropdown = [
    // { text: 'Support', icon: 'help_outline', onClick: () => window.alert('Open support ticket...') },
    { text: 'Settings', icon: 'settings', onClick: () => navigate('/settings') },
    { text: 'Log out', icon: 'logout', onClick: () => logout() },
  ]

  if (checkRole('super-admin')) {
    dropdown.push({ text: 'Roles', icon: 'lock', onClick: () => navigate('/roles') })
  }

  return (
    <div className={'dark flex'}>
      <Sidebar
        pathname={pathname}
        header={{
          logo: <FullLogo className='h-auto w-full cursor-pointer' />,
          collapsedLogo: <SmallLogo className='h-8 w-8 cursor-pointer' />,
          onLogoClick: () => navigate('/dashboard'),
        }}
        footer={{
          name: user?.name ?? '',
          organization: user?.organization?.name,
          photoURL: user?.profile_image ?? null,
          dropdownContent: dropdown,
        }}
        links={[
          {
            to: '/dashboard',
            text: 'Dashboard',
            icon: 'dashboard',
          },
          {
            to: '/satellite',
            text: 'Satellite Queue',
            icon: 'rocket',
            show: checkPermission('view-satellite'),
          },
          {
            to: '/titles',
            text: 'Titles',
            icon: 'movie',
            show: checkPermission('view-titles'),
          },
          {
            to: '/releases',
            text: 'Releases',
            icon: 'inventory',
            show: checkPermission('view-releases'),
          },
          {
            to: '/sites',
            text: 'Sites',
            icon: 'theaters',
            show: checkPermission('view-cinemas'),
          },
          {
            to: '/bookings',
            text: 'Bookings',
            icon: 'grading',
            show: checkPermission('view-bookings'),
          },
          {
            to: '/transfers',
            text: 'Transfers',
            icon: 'cloud_downloads',
            show: checkPermission('view-deliveries'),
          },
          {
            to: '/equipment',
            text: 'Equipment',
            icon: 'dns',
            show: checkPermission('view-equipment'),
          },
          {
            to: '/organizations',
            text: 'Organizations',
            icon: 'domain',
            show: checkPermission('view-organizations'),
          },
          {
            to: '/reports',
            text: 'Reports',
            icon: 'insert_chart_outlined',
            show: checkPermission('view-reports'),
          },
          {
            to: '/users',
            text: 'Users',
            icon: 'people',
            show: checkPermission('view-users'),
          },
        ].map((opt) => ({
          ...opt,
          onClick: () => (opt.onClick ? opt.onClick() : navigate(opt.to)),
        }))}
      ></Sidebar>
    </div>
  )
}

export default Navigation

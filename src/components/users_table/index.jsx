import React, { useState } from 'react'
import { Table, Tag } from '@bitcine/cinesend-theme'
import { convertToLocal, convertToLocalDateTime } from '/src/helpers/convert_date'
import { useNavigate } from 'react-router-dom'
import ResetTableFilters from '/src/components/reset_table_filters';
import { useQuery } from '@tanstack/react-query'

function UsersTable ({ apiUrl, orgId = null }) {

  const navigate = useNavigate()
  const headerDefaults = { search: '', page: 0, take: 15, sortBy: 'created_at', sortDirection: 'asc' }
  const defaults = localStorage.getItem(apiUrl) !== null
    ? JSON.parse(localStorage.getItem(apiUrl))
    : headerDefaults

  const [queries, setQueries] = useState(defaults)

  const params = new URLSearchParams(queries).toString()
  const url = `${apiUrl}?${params}`

  const setQuery = (data) => {
    const newQueries = { ...queries, ...data }
    // if a page wasn't clicked, and anything else was changed reset page to 0.
    if (data && !data.page) {
      newQueries.page = 0
    }
    setQueries(newQueries)
    localStorage.setItem(apiUrl, JSON.stringify(newQueries))
  }

  const { data: users, error } = useQuery({ queryKey: [url], refetchOnWindowFocus: false})

  return (
    <Table
      status={{
        pending: !users,
        pendingMessage: 'Loading users...',
        error: error
      }}
      header={{
        customElement: <ResetTableFilters filterCallback={() => {
          setQuery(headerDefaults, false, true);
        }}
        />,
        columns: [
          { text: 'Name', key: 'name' },
          { text: 'Email', key: 'email' },
          { text: 'Status' },
          { text: 'Last Login', key: 'last_login_at' },
          { text: 'Created Date', key: 'created_at' }
        ],
        searching: {
          search: queries.search,
          searchPlaceholder: 'Search...',
          onSearch: search => setQuery({ search }),
          rightIcon: {
            icon: 'close',
            onClick: () => setQuery({search: ''}),
            tooltip: {
              text: "Clear search"
            }
          }
        },
        sorting: {
          options: [
            { key: 'name', label: 'Name' },
            { key: 'email', label: 'Email' },
            { key: 'last_login_at', label: 'Last Login Date' },
            { key: 'created_at', label: 'Created Date' }
          ],
          key: queries.sortBy,
          sortingKey: queries.sortBy,
          direction: queries.sortDirection,
          onSortChange: (sort) => setQuery({ sortBy: sort.key, sortDirection: sort.direction })
        }
      }}
      widths={[160, 200, 100, 90, 90]}
      body={{
        data: users?.data,
        row: {
          spaced: true,
          onClick: (event, data) => navigate(`/users/${data.id}`),
          render: [
            ({ name, organization }) => <div className='flex flex-col'>
              <span className='font-medium'>{name}</span>
              {organization ? <span className='text-xs font-light'>{organization.name}</span> : null}
            </div>,
            ({ email }) => <div className='truncate'>{email}</div>,
            ({ status }) => <Tag
              outline
              type={status === 'active' ? 'success' : 'primary'}
              label={<div className='capitalize'>{status}</div>}/>,
            ({ last_login_at }) => convertToLocalDateTime(last_login_at),
            ({ created_at }) => convertToLocal(created_at)
          ]
        },
        empty: {
          title: 'No users found!',
          text: 'Refine your filters.',
          icon: 'people'
        }
      }}
      paginate={{
        totalRows: users?.total,
        currentPage: users?.current_page - 1,
        rowsPerPage: users?.per_page,
        onPageChange: page => setQuery({ page }),
        onRowsPerPageChange: take => setQuery({ take })
      }}
    />
  )
}

export default UsersTable

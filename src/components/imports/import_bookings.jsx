import { Button } from '@bitcine/cinesend-theme'
import { useState } from 'react'
import ImportModal from './import_modal'

const ImportBookings = () => {

  const [importModalOpen, setImportModalOpen] = useState(false)

  return (
    <>
      <Button icon='upload' onClick={() => setImportModalOpen(true)}>
        Upload bookings
      </Button>
      {importModalOpen &&
        <ImportModal onClose={() => setImportModalOpen(false)}/>}
    </>
  )
}

export default ImportBookings

import { Button } from '@bitcine/cinesend-theme/dist'
import { useSnackbar } from '/src/hooks/snackbar'
import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import axios from '/src/lib/axios'

function ImportDetails ({ importRecord }) {

  const navigate = useNavigate()
  const [pending, setPending] = useState(false)
  const { addMessage } = useSnackbar()

  return (
    <>
      <button
        className="underline text-lg font-semibold"
        onClick={() => navigate(`/titles/${importRecord.title.id}`)}>
        {importRecord.title.friendly_title}
      </button>
      <p className="my-2 text-sm">
        <ul className="list-disc ml-6 my-2 nowrap">
          {importRecord.release ? <>
            Release: <button
            onClick={() => navigate(`/titles/${importRecord.title.id}/releases/${importRecord.release.id}`)}>{importRecord.release.package_name}</button>
          </> : <>
            Content:
            {importRecord.version_ids.map(item =>
              <li key={item.value}>
                <button onClick={() => navigate(`/titles/${importRecord.title.id}/versions/${item.value}`)}>{item.label}</button>
              </li>)}
          </>}
        </ul>
      </p>
      <Button
        disabled={pending || importRecord.matched_count === 0}
        onClick={() => {
          setPending(true)
          addMessage('Creating bookings now! This operation will complete in the background.')
          axios.get(`/api/import-bookings/${importRecord?.id}/start`)
            .then(res => setPending(false))
        }}>
        Create {importRecord.matched_count} booking{importRecord.matched_count === 1 ? '' : 's'}
      </Button>
    </>
  )

}

export default ImportDetails

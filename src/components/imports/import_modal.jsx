import dayjs from 'dayjs'
import axios from '/src/lib/axios'
import { DatePicker, Modal, Select, FileUpload } from '@bitcine/cinesend-theme'
import { useState } from 'react'
import ErrorMessages from '/src/components/error_messages'
import { v4 as uuid } from 'uuid'
import ImportProgress from './import_progress'
import { useQuery } from '@tanstack/react-query'

const ImportModal = ({ onClose }) => {

  const [titleID, setTitleID] = useState(null)
  const [versionIDs, setVersionIDs] = useState([])
  const [releaseID, setReleaseID] = useState()
  const [deliveryDate, setDeliveryDate] = useState(null)
  const [releaseDate, setReleaseDate] = useState(null)
  const [pending, setPending] = useState(false)
  const [file, setFile] = useState({})
  const [destinationUrl, setDestinationUrl] = useState('')
  const [mapping, setMapping] = useState({})
  const [importRecord, setImportRecord] = useState({})

  const titlesUrl = '/api/titles?take=500'
  const { data: titles } = useQuery({ queryKey: [titlesUrl]})
  const titleOptions = titles ? titles.data.map(title => ({ label: title.friendly_title, value: title.id })) : []

  const versionsUrl = `/api/titles/${titleID}/versions?take=100`
  const { data: versions } = useQuery({ queryKey: [versionsUrl]})

  const releasesUrl = `/api/titles/${titleID}/releases?take=100`
  const { data: releases } = useQuery({ queryKey: [releasesUrl]})


  const versionOptions = versions
    ? versions.data.map(version => ({ label: version.version_name, value: version.id }))
    : []

  const releaseOptions = releases
    ? releases.data.map(pkg => ({ label: pkg.package_name, value: pkg.id }))
    : []

  const nextStep = () => {
    // submit the form data to update (and enqueue the processor)
    // switch to 'progress view mode'
    setPending(true)
    axios.put(destinationUrl, {
      release_date: deliveryDate, // release date is delivery date in db.
      engagement_start: releaseDate,
      title_id: titleID,
      version_ids: versionIDs,
      package_id: releaseID?.value,
      mapping
    }).then(res => {
      setImportRecord(res.data.data)
      if (res.data.error_message) {
        setFile({})
      }
    }).catch(error => {}).finally(() => setPending(false))
  }

  return (
    <Modal
      className="w-1/2"
      header="Import New Bookings"
      onClose={onClose}
      confirmButton={{
        text: 'Start Import',
        disabled: pending || (!versionIDs.length && !releaseID?.value) || !titleID
          || JSON.stringify(mapping) === '{}' || (importRecord && importRecord.verified),
        onClick: () => nextStep()
      }}>
      {(!importRecord || !importRecord.verified) &&
        <div className="flex flex-col space-y-4">
          <Select
            label="Title"
            disabled={importRecord && importRecord.verified}
            options={titleOptions}
            value={titleOptions.find(opt => opt.value === titleID)}
            onChange={opt => {
              setVersionIDs([])
              setTitleID(opt.value)
            }}/>
          <Select
            label="Select Content:"
            description="Select one or more contents to distribute."
            isMulti
            isClearable
            disabled={!titleID || releaseID?.value || (importRecord && importRecord.verified)}
            options={versionOptions}
            value={versionIDs}
            onChange={opt => { setVersionIDs(opt) }}/>
          <Select
            label="Or a single Release:"
            description="Select a single release to distribute."
            isClearable
            disabled={!titleID || versionIDs.length > 0 || (importRecord && importRecord.verified)}
            options={releaseOptions}
            value={releaseID}
            onChange={opt => { setReleaseID(opt) }}/>
          <DatePicker
            label="Delivery Date"
            showJumpToToday={true}
            minDate={dayjs()}
            disabled={importRecord && importRecord.verified}
            date={deliveryDate}
            showTimeSelect={false}
            onChange={date => setDeliveryDate(date)}/>
          <DatePicker
            label="Engagement Date"
            showJumpToToday={true}
            minDate={dayjs()}
            disabled={importRecord && importRecord.verified}
            date={releaseDate}
            showTimeSelect={false}
            onChange={date => setReleaseDate(date)}/>

          {importRecord && importRecord.headers && !importRecord.verified && <>
            <p className="my-2 font-medium text-sm">Please map at least one site identifier with a column header:</p>
            <div className="grid grid-cols-2 gap-2">
              {importRecord.lookup.map((lookup =>
                (<Select
                  key={uuid()}
                  label={lookup.label}
                  name={lookup.value}
                  options={importRecord.headers.map((header => ({ label: header, value: header })))}
                  value={mapping[lookup.key]}
                  onChange={opt => {
                    setMapping({ ...mapping, [lookup.key]: opt })
                  }}/>)
              ))}
            </div>
          </>}
          {importRecord && !importRecord.headers && <FileUpload
            className="w-full"
            upload={{
              // This upload happens immediately. It will create the import record and return the ID/uRL required
              // to fetch the data.
              icon: 'document_scanner',
              message: 'Upload an XLS, XLSX, CSV, or ODS file here.',
              apiURL: `${import.meta.env.VITE_BACKEND_URL}/api/import-bookings`,
              onComplete: ((file, destinationUrl) => {
                setDestinationUrl(destinationUrl)
                setPending(true)
                setFile({ selected: file })
                // fetch destination url for updated data
                axios.get(destinationUrl).then(res => {
                  setImportRecord(res.data.data)
                  if (res.data.error_message) {
                    setFile({})
                  }
                }).catch(error => {}).finally(() => setPending(false))
              }),
              accept: [
                'text/csv',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.oasis.opendocument.spreadsheet']
            }}
            button={{ text: 'Upload File' }}
            file={file.selected}
          />}
          {importRecord && importRecord.error_message &&
            <ErrorMessages errors={[importRecord.error_message]}/>
          }
        </div>
      }
      {importRecord && importRecord.verified && <>
        <ImportProgress updateUrl={destinationUrl}/>
      </>}
    </Modal>
  )
}

export default ImportModal

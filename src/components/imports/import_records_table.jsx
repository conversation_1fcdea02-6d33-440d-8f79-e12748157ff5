import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Table } from '@bitcine/cinesend-theme'
import ImportCinemaSelectModal from './import_cinema_select_modal'
import { useQuery } from '@tanstack/react-query'
import ResetTableFilters from '/src/components/reset_table_filters'

function ImportRecordsTable ({ apiUrl }) {

  const navigate = useNavigate()

  const headerDefaults = { search: '', page: 0, take: 15, sortBy: 'created_at', sortDirection: 'DESC' }

  const defaults = localStorage.getItem(apiUrl) !== null
    ? JSON.parse(localStorage.getItem(apiUrl))
    : headerDefaults

  const [queries, setQueries] = useState(defaults)
  const [modalActive, setModalActive] = useState(false)
  const [activeRecord, setActiveRecord] = useState({})

  const params = new URLSearchParams(queries).toString()
  const url = `${apiUrl}?${params}`

  const setQuery = (data) => {
    const newQueries = { ...queries, ...data }
    // if a page wasn't clicked, and anything else was changed reset page to 0.
    if (data && !data.page) {
      newQueries.page = 0
    }
    setQueries(newQueries)
    localStorage.setItem(apiUrl, JSON.stringify(newQueries))
  }

  const { data: records, isLoading, error } = useQuery({ queryKey: [url], refetchInterval: 10000 }) 

  return (
    <>
      <Table
        status={{
          pending: !records || isLoading,
          pendingMessage: 'Loading Imports...',
          error: error
        }}
        header={{
          customElement: <ResetTableFilters filterCallback={() => {
            setQuery(headerDefaults, false, true);
          }}
          />,
          columns: [
            { text: 'Raw Data' },
            { text: 'Matched Site' }
          ],
          searching: {
            search: queries.search,
            searchPlaceholder: 'Search...',
            onSearch: search => setQuery({ search }),
            rightIcon: {
              icon: 'close',
              onClick: () => setQuery({search: ''}),
              tooltip: {
                text: "Clear search"
              }
            }
          },
          sorting: {
            options: [],
            key: queries.sortBy,
            sortingKey: queries.sortBy,
            direction: queries.sortDirection,
            onSortChange: (sort) => setQuery({ sortBy: sort.key, sortDirection: sort.direction })
          }
        }}
        widths={['auto', '400']}
        body={{
          data: records?.data,
          row: {
            spaced: true,
            onClick: (event, data) => {
              if (data.booking_id) {
                // booking already exists so this record is 'locked'
                navigate(`/bookings/${data.booking_id}`)
              }
              else {
                // set/edit the destination theatre via modal lookup.
                setActiveRecord(data)
                setModalActive(true)
              }
            },
            render: [
              ({ original_record }) => <div className="text-xs">
                {
                  Object.entries(original_record).map(([key, val]) =>
                    `${key}: ${val}, `
                  )
                }
              </div>,
              ({ cinema }) => <div className="w-full text-xs">{cinema?.name ?? 'N/A'}</div>
            ]
          },
          empty: {
            title: 'No records found!',
            text: 'Refine your filters.',
            icon: 'cloud_sync'
          }
        }}
        paginate={{
          totalRows: records?.total,
          currentPage: records?.current_page - 1,
          rowsPerPage: records?.per_page,
          onPageChange: page => setQuery({ page }),
          onRowsPerPageChange: take => setQuery({ take })
        }}
      />
      {modalActive && <ImportCinemaSelectModal onClose={() => setModalActive(false)} target={activeRecord}/>}
    </>
  )
}

export default ImportRecordsTable

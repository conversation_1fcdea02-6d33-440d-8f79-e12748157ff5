import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Table, Tag } from '@bitcine/cinesend-theme'
import { useQuery } from '@tanstack/react-query'
import ResetTableFilters from '/src/components/reset_table_filters'

function ImportTable ({ apiUrl }) {

  const navigate = useNavigate()

  const headerDefaults = { search: '', page: 0, take: 15, sortBy: 'created_at', sortDirection: 'DESC' }

  const defaults = localStorage.getItem(apiUrl) !== null
    ? JSON.parse(localStorage.getItem(apiUrl))
    : headerDefaults

  const [queries, setQueries] = useState(defaults)

  const params = new URLSearchParams(queries).toString()
  const url = `${apiUrl}?${params}`

  const setQuery = (data) => {
    const newQueries = { ...queries, ...data }
    // if a page wasn't clicked, and anything else was changed reset page to 0.
    if (data && !data.page) {
      newQueries.page = 0
    }
    setQueries(newQueries)
    localStorage.setItem(apiUrl, JSON.stringify(newQueries))
  }

  const { data: imports, isLoading, error } = useQuery({ queryKey: [url], refetchInterval: 10000})

  return (
    <Table
      status={{
        pending: !imports || isLoading,
        pendingMessage: 'Loading Imports...',
        error: error
      }}
      header={{
        customElement: <ResetTableFilters filterCallback={() => {
          setQuery(headerDefaults, false, true);
        }}
        />,
        columns: [
          { text: 'Filename' },
          { text: '# Pending Matches' },
          { text: '# Matched Sites' },
          { text: '# Bookings Created' }
        ],
        searching: {
          search: queries.search,
          searchPlaceholder: 'Search...',
          onSearch: search => setQuery({ search }),
          rightIcon: {
            icon: 'close',
            onClick: () => setQuery({search: ''}),
            tooltip: {
              text: "Clear search"
            }
          }
        },
        sorting: {
          options: [],
          key: queries.sortBy,
          sortingKey: queries.sortBy,
          direction: queries.sortDirection,
          onSortChange: (sort) => setQuery({ sortBy: sort.key, sortDirection: sort.direction })
        }
      }}
      widths={[200, 100, 100, 100]}
      body={{
        data: imports?.data,
        row: {
          spaced: true,
          onClick: (event, data) => navigate(`/imports/${data.id}`),
          render: [
            ({ origin_name }) => origin_name,
            ({ pending_count }) => <Tag size='large' type='warning' label={pending_count}/>,
            ({ matched_count }) => <Tag size='large' type='info' label={matched_count}/>,
            ({ booked_count }) => <Tag size='large' type='success' label={booked_count}/>
          ]
        },
        empty: {
          title: 'No bookings found!',
          text: 'Refine your filters.',
          icon: 'cloud_sync'
        }
      }}
      paginate={{
        totalRows: imports?.total,
        currentPage: imports?.current_page - 1,
        rowsPerPage: imports?.per_page,
        onPageChange: page => setQuery({ page }),
        onRowsPerPageChange: take => setQuery({ take })
      }}
    />
  )
}

export default ImportTable

import { useNavigate } from 'react-router-dom'
import { ProgressBar, Button } from '@bitcine/cinesend-theme'
import { useQuery } from '@tanstack/react-query'

const ImportProgress = ({ updateUrl }) => {

  const navigate = useNavigate()

  const { data: importRecord } = useQuery({ queryKey: [updateUrl] })

  return (
    <>
      {importRecord &&
        <>
          <p>Pre-Processing {importRecord.origin_name}</p>
          <ProgressBar completed={(importRecord.num_records_processed / importRecord.num_records) * 100}/>
          {((importRecord.num_records_processed / importRecord.num_records) * 100 >= 100) &&
            <div className="my-4">
              <Button
                onClick={() => navigate(`/imports/${importRecord.id}`)}>Confirm sites</Button>
            </div>
          }
        </>
      }
    </>
  )
}

export default ImportProgress

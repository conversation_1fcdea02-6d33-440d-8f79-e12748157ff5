import axios from '/src/lib/axios'
import { Modal } from '@bitcine/cinesend-theme'
import MiniSitesTable from '/src/components/sites/mini_table'
import { useSnackbar } from '/src/hooks/snackbar'

const ImportCinemaSelectModal = ({ onClose, target }) => {
  const { addMessage } = useSnackbar()
  return (
    <Modal
      className="w-1/2"
      header="Select Cinema"
      onClose={onClose}>
      <div className="flex flex-col space-y-4">
        <p>Use the search to filter down the results. Click the row to assign the cinema to this import record.</p>
        <MiniSitesTable onClick={(data) => {
          addMessage('Matching site! This table will automatically refresh.')
          axios.put(`api/import-booking-records/${target.id}`, {
            cinema_site_id: data.id
          }).then(res => {
            onClose()
          }).catch(error => {
          }).finally(() => {})
        }}/>
      </div>
    </Modal>
  )
}

export default ImportCinemaSelectModal

import React, { useCallback, useEffect } from 'react'
import Icon from '@bitcine/cinesend-theme/dist/icon'

const Slide = ({ header, onClose, children }) => {
  const escFunction = useCallback((event) => {
    if (event.key === 'Escape') {
      onClose()
    }
  }, [onClose])
  useEffect(() => {
    document.addEventListener('keydown', escFunction, false)
    return () => {
      document.removeEventListener('keydown', escFunction, false)
    }
  }, [escFunction])
  return (
    <div className="z-10 fixed inset-0 bg-black/50">
      <div className="fixed top-0 right-0 bottom-0 w-full md:w-1/2 lg:w-2/5 border-l border-primary bg-white">
        <div className="flex flex-row-reverse justify-between p-8 border-b">
          <Icon icon="close" onClick={onClose} className="text-gray-600"/>
          {header && <h4>{header}</h4>}
        </div>
        <div className='p-8 h-screen overflow-y-auto'>
          {children}
        </div>
      </div>
    </div>
  )
}

export default Slide

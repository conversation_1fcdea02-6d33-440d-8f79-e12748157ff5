import { useState } from 'react'
import { Input, Checkbox, Button } from '@bitcine/cinesend-theme'
import { v4 as uuid } from 'uuid'

const RolesList = ({ roleId = null, userId = null, list, onCreate = (name) => {}, onCheck = (roleId) => {} }) => {
  const [name, setName] = useState('')

  return (
    <div className={'w-1/2 bg-white'}>
      <h6 className={'mt-4 mb-2'}>Select a Role to Manage</h6>
      {list.map((r, index) =>
        <li key={uuid()} className={`list-none p-2 cursor-pointer ${roleId === r.id ? 'bg-green-300' : ''}`}
          onClick={() => onCheck(r.id)}>{r.name}
        </li>)}
      <h6 className={'mt-4 mb-2'}>Create a new Role</h6>
      <Input
        value={name}
        onChange={(e) => setName(e.target.value)} label={'Role Name'}/>
      <Button onClick={() => {
        onCreate(name)
        setName('')
      }}>Save</Button>
    </div>
  )

}

export default RolesList

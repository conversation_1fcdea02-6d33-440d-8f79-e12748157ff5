import React, { useState } from 'react'
import { Button, Input } from '@bitcine/cinesend-theme'
import { useUsers } from '/src/hooks/users'
import ErrorMessages from '/src/components/error_messages'
import TwoFactor from './2fa/app_2fa'

const Security = ({ user }) => {
  const [errors, setErrors] = useState([])
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [pending, setPending] = useState(false)
  const { updateUser } = useUsers()
  const save = () => {
    setPending(true)
    updateUser({
      userID: user.id,
      password: newPassword,
      password_confirmation: confirmPassword,
      onComplete: () => {
        setPending(false)
        setNewPassword('')
        setConfirmPassword('')
      },
      setErrors: errors => {
        setPending(false)
        setErrors(errors)
      }
    })
  }
  return (
    <div className='flex flex-col space-y-4 max-w-3xl'>
      <Input
        label='New Password'
        type='password'
        value={newPassword}
        onChange={e => setNewPassword(e.target.value)}
      />
      <Input
        label='Confirm New Password'
        type='password'
        value={confirmPassword}
        onChange={e => setConfirmPassword(e.target.value)}
      />
      <div className='flex justify-end'>
        <Button
          onClick={save}
          disabled={newPassword !== confirmPassword || !newPassword || pending}>
          Change Password
        </Button>
      </div>
      <ErrorMessages errors={errors}/>

      <TwoFactor/>
    </div>
  )

}

export default Security

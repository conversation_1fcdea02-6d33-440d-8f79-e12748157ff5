import { Input, Checkbox, Button } from '@bitcine/cinesend-theme'
import { useEffect, useMemo, useRef, useState } from 'react'
import { v4 as uuid } from 'uuid'

const doSortPermissions = (permissions) => permissions.sort((a, b) => a.name - b.name) // b - a for reverse sort

const PermissionsList = ({ roleId, list, selected, onSave = (permissions) => {}, onCreate = (name) => {} }) => {

  const [permissions, setPermissions] = useState(selected) // use this to post.
  const [name, setName] = useState('')

  const removeFromSelected = (permission) => {
    let perms = permissions.filter((perm) => perm.id !== permission.id)
    setPermissions(perms)
  }

  const addToSelected = (permission) => {
    let perms = permissions.filter(() => true)
    perms.push(permission)
    setPermissions(perms)
  }

  const permissionInSelected = (permissionId) => permissions.find((p) => p.id === permissionId)

  const noDifference = () => {
    let perms = permissions.filter(() => true)
    let origin = selected.filter(() => true)

    perms.sort((a, b) => a.id - b.id)
    origin.sort((a, b) => a.id - b.id)

    return JSON.stringify(perms) === JSON.stringify(origin)
  }

  // track external changes
  useEffect(() => {
    setPermissions(selected)
  }, [selected])

  return (
    roleId ?
      <div className={'w-1/2 bg-white'}>
        <h6 className={'mt-4 mb-2'}>Allowed Permissions ({permissions.length} of {list.length})</h6>
        {list.filter(p => permissionInSelected(p.id)).map(p => <Checkbox
          key={uuid()}
          className={'w-1/3'}
          checked={true}
          label={p.name}
          onChange={() => removeFromSelected(p)}
        />)}
        <h6 className={'mt-4 mb-2'}>Available Permissions</h6>
        {list.filter(p => !permissionInSelected(p.id)).map(p => <Checkbox
          key={uuid()}
          className={'w-1/3'}
          checked={false}
          label={p.name}
          onChange={() => addToSelected(p)}
        />)}
        <h6 className={'mt-4 mb-2'}>Confirm Permissions</h6>
        <Button disabled={noDifference()} onClick={() => onSave(permissions)}>Save</Button>

        <h6 className={'mt-4 mb-2'}>Create a new Permission</h6>
        <Input
          value={name}
          onChange={(e) => setName(e.target.value)} label={'Permission Name'}/>
        <Button onClick={() => {
          onCreate(name)
          setName('')
        }}>Save</Button>

      </div> : <></>
  )
}

export default PermissionsList

import axios from '/src/lib/axios'
import React, { useState } from 'react'
import { Button, Input, ImageUpload, FileUpload } from '@bitcine/cinesend-theme'
import { useUsers } from '/src/hooks/users'
import ErrorMessages from '/src/components/error_messages'
import getUpdatedData from '/src/helpers/get_updated_data'
import { useAuth } from '../../hooks/auth'

const Details = () => {
  const { user, mutate } = useAuth()

  const [errors, setErrors] = useState([])
  const [data, setData] = useState(user)
  const [pending, setPending] = useState(false)

  const update = newData => {
    setData({ ...data, ...newData, setErrors })
  }
  const { updateUser } = useUsers()
  const save = () => {
    setPending(true)
    updateUser({
      userID: user.id, ...getUpdatedData(user, data),
      onComplete: () => {
        setPending(false)
      }, setErrors
    })
  }
  return (
    <div className="flex flex-col max-w-3xl">
      <div className="flex flex-row space-x-4">
        <FileUpload
          className={'w-1/4 h-40'}
          backgroundImage={{
            url: user.profile_image
          }}
          includeRemoveButton={false}
          upload={{
            message: 'Drop image here',
            accept: 'image/*',
            icon: 'image',
            apiURL: `${import.meta.env.VITE_BACKEND_URL}/api/user/upload-profile`,
            onComplete: ((file, destinationUrl) => {
              mutate()
            })
          }}
          button={{
            text: 'Upload'
          }}
        />
        <div className="w-3/4 flex flex-col space-y-4 ">
          <Input
            label="Full Name"
            value={data.name}
            onChange={e => update({ name: e.target.value })}
          />
          <Input
            label="Email"
            value={data.email}
            onChange={e => update({ email: e.target.value })}
          />
          {user.needs_password &&
            <>
              <div className={'text-red-500'}>Please set a new password to fully access portal features.</div>
              <Input
                label="Password"
                type={'password'}
                value={data.password}
                onChange={e => update({ password: e.target.value })}
              />
              <Input
                label="Confirm Password"
                type={'password'}
                value={data.password_confirmation}
                onChange={e => update({ password_confirmation: e.target.value })}
              /></>}
        </div>
      </div>
      <div className="flex justify-end mt-2">
        <Button
          onClick={save}
          disabled={JSON.stringify(data) === JSON.stringify(user) || pending}>
          Save
        </Button>
      </div>
      <ErrorMessages errors={errors}/>
    </div>
  )

}

export default Details

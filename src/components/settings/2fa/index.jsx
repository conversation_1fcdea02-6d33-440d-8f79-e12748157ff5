import { SegmentedControl } from '@bitcine/cinesend-theme'
import AppTwoFactor from './app_2fa'
import SMSTwoFactor from './sms_2fa'
import TwoFactorEnabled from './2fa_enabled'
import { useAuth } from '/src/hooks/auth'
import { useState } from 'react'

function TwoFactor () {
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState('sms_based')
  return (
    <div className='border rounded p-4'>
      {user.two_factor_activated ? <TwoFactorEnabled/> :
        <div className='flex flex-col space-y-4'>
          <SegmentedControl
            options={[
              { label: 'SMS-based 2FA', value: 'sms_based' },
              { label: 'App-based 2FA', value: 'app_based' }
            ]}
            value={activeTab}
            onChange={setActiveTab}
            type={'primary'}
          />
          {activeTab === 'app_based' && <AppTwoFactor/>}
          {activeTab === 'sms_based' && <SMSTwoFactor/>}
        </div>
      }
    </div>
  )
}

export default TwoFactor

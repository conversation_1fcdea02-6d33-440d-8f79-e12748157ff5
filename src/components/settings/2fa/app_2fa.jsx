import AppLayout from '/src/components/layouts/app_layout'
import AuthSessionStatus from '/src/components/auth_session_status'
import AuthValidationErrors from '/src/components/auth_validation_errors'
import { Input, Button } from '@bitcine/cinesend-theme'
import { useAuth } from '/src/hooks/auth'
import { useState } from 'react'
import Icon from '@bitcine/cinesend-theme/dist/icon'
import { useQuery, useQueryClient } from '@tanstack/react-query'

const TwoFactor = () => {

  const { user, activateTwoFactor, verifyTwoFactorActivation } = useAuth({
    middleware: 'auth'
  })

  // if a user is authed, they can activate 2FA
  // the post to /user/two-factor-authentication turns it on
  // a get to /user/two-factor-qr-code returns an SVG & OTP protocol URL
  // then a post the code to /user/confirmed-two-factor-authentication verifies it
  // then a get to /user/two-factor-recovery-codes for a list of backups
  // then it's activated.

  const [code, setCode] = useState('')
  const [errors, setErrors] = useState([])
  const [pending, setPending] = useState(false)
  const [status, setStatus] = useState(null)

  const qrKey = '/user/two-factor-qr-code'
  const recoveryKey = '/user/two-factor-recovery-codes'

  const queryClient = useQueryClient()

  const { data: qrCodeData } = useQuery({ queryKey: [qrKey]})

  const mutate = (key) => queryClient.invalidateQueries({ queryKey: [key]})

  const { data: recoveryData } = useQuery({ queryKey: [recoveryKey] })
  
  const verificationForm = async event => {
    event.preventDefault()
    await verifyTwoFactorActivation({ code, setErrors, setStatus, setPending })
    mutate(qrKey)
  }

  const activationForm = async event => {
    event.preventDefault()
    await activateTwoFactor({ setErrors, setStatus, setPending })
    mutate(recoveryKey)
  }

  return (
    <>
      {!user.two_factor_pending &&
        <>
          <p className={'my-4'}>In order to activate app-based two-factor authentication (2FA) you need a piece of
            software that can generate one-time-passwords (OTP) in order to sign in. Recommended:</p>
          <ul className={'list list-disc ml-4 my-1'}>
            <li>1Password</li>
            <li>LastPass</li>
            <li>Google Authenticator</li>
            <li>Microsoft Authenticator</li>
          </ul>
          <form onSubmit={activationForm} className="space-y-4">
            <div className="flex justify-end">
              <Button type="warning">
                Enable app-based 2FA
              </Button>
            </div>
          </form>
        </>
      }
      {user.two_factor_pending &&
        <div className="space-y-4">
          <p>Scan this QR code into your authenticator app, then enter the 6-digit code in the input below.</p>
          {qrCodeData &&
            <>
              <div dangerouslySetInnerHTML={{ __html: qrCodeData.svg }}/>
              {/* <a href={qrCodeData.url}>Add to your Password App</a> */}
            </>
          }

          <form onSubmit={verificationForm} className="space-y-4 mt-2">
            <Input
              label="Code"
              value={code}
              onChange={event => setCode(event.target.value)}
            />
            <div className="flex items-center justify-end">
              <Button type="warning" disabled={!code}>
                Verify two-factor code
              </Button>
            </div>
          </form>
        </div>
      }
    </>
  )
}

export default TwoFactor

import { Button } from '@bitcine/cinesend-theme'
import Icon from '@bitcine/cinesend-theme/dist/icon'
import { useAuth } from '/src/hooks/auth'

function TwoFactorEnabled () {
  const { removeTwoFactor } = useAuth()
  return (
    <>
      <div className="flex flex-col space-y-4">
        <div className="flex flex-col justify-center items-center space-y-4 py-8">
          <Icon icon="check_circle_outline" className="text-success-600 text-5xl"/>
          <p>Activated!</p>
        </div>
        <div className="flex justify-end w-full">
          <Button type="error" secondary
            onClick={() => {
              if (window.confirm('Are you sure you want to remove two-factor authentication?')) {
                removeTwoFactor({})
              }
            }}>
            Disable two-factor authentication
          </Button>
        </div>
      </div>
    </>
  )
}

export default TwoFactorEnabled

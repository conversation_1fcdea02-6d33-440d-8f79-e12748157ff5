import { Input, But<PERSON>, Status } from '@bitcine/cinesend-theme'
import ErrorMessages from '/src/components/error_messages'
import { useAuth } from '/src/hooks/auth'
import { useState } from 'react'
import { useQueryClient } from '@tanstack/react-query'

const Sms2fa = () => {

  const { user, activateSmsTwoFactor, verifySmsTwoFactor } = useAuth({
    middleware: 'auth'
  })

  const [code, setCode] = useState('')
  const [phoneNumber, setPhoneNumber] = useState('')
  const [errors, setErrors] = useState([])
  const [pending, setPending] = useState(false)
  const [status, setStatus] = useState(null)
  const queryClient = useQueryClient()
  const mutate = () => queryClient.invalidateQueries({ queryKey: ['/api/user']})


  const verificationForm = async event => {
    event.preventDefault()
    await verifySmsTwoFactor({ code, setErrors, setStatus, setPending })
    mutate()
  }

  const activationForm = async event => {
    event.preventDefault()
    await activateSmsTwoFactor({ phone_number: phoneNumber, setErrors, setStatus, setPending })
    mutate()
  }

  return (
    <Status pending={pending}>
      <div className='flex flex-col space-y-4'>
        {!user.two_factor_sms_pending &&
          <>
            <p className={'my-4'}>In order to activate SMS-based two-factor authentication (2FA)
            we need to verify your phone number. Please enter it below:</p>
            <form onSubmit={activationForm} className="space-y-4">
              <Input label={'Phone Number'} value={phoneNumber} onChange={event => setPhoneNumber(event.target.value)}/>
              <div className="flex justify-end">
                <Button type="warning" disabled={!phoneNumber}>
                  Enable SMS-based 2FA
                </Button>
              </div>
            </form>
          </>
        }
        {user.two_factor_sms_pending &&
          <>
            <p>Please enter the code you received via SMS below:</p>
            <form onSubmit={verificationForm} className="space-y-4 mt-2">
              <Input
                label="Code"
                value={code}
                onChange={event => setCode(event.target.value)}
              />
              <div className="flex justify-end">
                <Button type="warning" disabled={!code}>
                  Verify two-factor code
                </Button>
              </div>
            </form>
          </>
        }
        <ErrorMessages errors={errors}/>
      </div>
    </Status>
  )
}

export default Sms2fa

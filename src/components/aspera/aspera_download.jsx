import { withAspera } from '/src/hooks/with_aspera'
import { Button } from '@bitcine/cinesend-theme'

function AsperaDownload ({ version }) {
  const { asperaWeb, createDownload } = withAspera()

  const setErrors = () => {}

  return (
    <>
      {!!version.is_ready && <Button
        icon="download"
        onClick={() => {
          let payload = {
            'title_id': version.title_id,
            'version_id': version.id
          }
          createDownload({setErrors, payload})
        }}>Download DCP</Button>}
    </>
  )

}

export default AsperaDownload

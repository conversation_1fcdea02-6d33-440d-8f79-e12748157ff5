import { useState } from 'react'
import { withAspera } from '/src/hooks/with_aspera'
import { Button, Modal, Input } from '@bitcine/cinesend-theme'
import { useTitles } from '/src/hooks/titles'
import { useSnackbar } from '/src/hooks/snackbar'
import { useAuth } from '/src/hooks/auth'

function AsperaUpload ({ title }) {

  const [errors, setErrors] = useState([])
  const [versionModalOpen, setVersionModalOpen] = useState(false)
  const [nickname, setNickname] = useState('')
  const [pending, setPending] = useState(false)

  const { createVersion, deleteVersion } = useTitles()
  const { addMessage } = useSnackbar()
  const { checkPermission } = useAuth()

  const create = () => {
    setPending(true)
    createVersion({
      titleID: title.id,
      setErrors: errors => {
        setPending(false)
        setErrors(errors)
      },
      onComplete: versionID => {

        const { asperaWeb, createUpload } = withAspera()

        setPending(false)
        setVersionModalOpen(false)
        setNickname('')
        asperaWeb.showSelectFolderDialog({
          success: ({ dataTransfer }) => {
            if (!dataTransfer.files || !dataTransfer.files.length) {
              addMessage('No folder selected. Please try again.', 'error')
              deleteVersion({
                versionID,
                titleID: title.id,
                setErrors: errors => {
                  setPending(false)
                  setErrors(errors)
                }
              })
              return false
            }
            let payload = {
              'aspera_data': dataTransfer,
              'title_id': title.id,
              'version_id': versionID
            }
            createUpload({ setErrors, payload })
          },
          error: res => {
            addMessage(res.error.user_message || 'Failed to upload, please try again', 'error')
          }
        })
      }
    })
  }

  return (<>
    <Button
      icon="upload"
      disabled={pending || !checkPermission('create-titles')}
      onClick={() => create()}>Upload DCP</Button>
  </>)

}

export default AsperaUpload

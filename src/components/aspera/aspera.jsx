export let asperaWeb, asperaInstaller, config

export const initAspera = (params) => {
  const { AW4 } = window
  const setErrors = () => {}

  config = {
    ...params,
    minVersion: params.minVersion || '3.8.0'
  }

  asperaWeb = new AW4.Connect({ sdkLocation: config.sdkLocation, minVersion: config.minVersion })
  asperaInstaller = new AW4.ConnectInstaller({ sdkLocation: config.sdkLocation })

  const statusEventListener = function (eventType, data) {
    if (eventType === AW4.Connect.EVENT.STATUS && data === AW4.Connect.STATUS.INITIALIZING) {
      // asperaInstaller.showLaunching()
    }
    else if (eventType === AW4.Connect.EVENT.STATUS && data === AW4.Connect.STATUS.FAILED) {
      asperaInstaller.showDownload()
    }
    else if (eventType === AW4.Connect.EVENT.STATUS && data === AW4.Connect.STATUS.OUTDATED) {
      asperaInstaller.showUpdate()
    }
    else if (eventType === AW4.Connect.EVENT.STATUS && data === AW4.Connect.STATUS.RUNNING) {
      asperaInstaller.connected()
    }
  }

  asperaWeb.addEventListener(AW4.Connect.EVENT.STATUS, statusEventListener)

  asperaWeb.addEventListener('transfer', (eventType, asperaObject) => {
    if (asperaObject.result_count > 0) {

      asperaObject.transfers.forEach((transfer) => {

        if (transfer.transfer_spec.tags['cinesend'].transfer_uuid) {
          switch (transfer.status) {
            case 'initiating':
            case 'failed':
            case 'completed':
            case 'running': // this one happens a lot.
              if (transfer.transfer_iteration_token % 30 === 0) {
                // refactor this to update a UI component, maybe. // updateStatus({ setErrors, transfer })
                // updateStatus({ setErrors, transfer })
              }
              break
            default:
              break
          }
        }
      })

    }
    switch (eventType) {
      case AW4.Connect.TRANSFER_STATUS.COMPLETED:
        break
      default:
        break
    }
  })

  asperaWeb.initSession()
}

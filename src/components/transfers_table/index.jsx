import { useEffect, useState } from 'react'
import { Tag, Table, ButtonDropdown } from '@bitcine/cinesend-theme'
import { useDeliveries } from '/src/hooks/deliveries'
import DeliveryType from '/src/components/icons/delivery_type'
import humanFileSize from '/src/helpers/human_file_size'
import { useAuth } from '/src/hooks/auth'
import { useQuery } from '@tanstack/react-query'
import ResetTableFilters from '/src/components/reset_table_filters'

function TransfersTable({ apiUrl, updateTitle = (title) => { } }) {

  const { showDelivery, deleteDelivery } = useDeliveries()
  const { checkPermission } = useAuth()

  const headerDefaults = { search: '', page: 0, take: 15, sortBy: 'created_at', direction: 'DESC' }
  const defaults = localStorage.getItem(apiUrl) !== null
    ? JSON.parse(localStorage.getItem(apiUrl))
    : headerDefaults

  const [queries, setQueries] = useState(defaults)
  const [checked, setChecked] = useState([])
  const [filter, setFilter] = useState({})

  const params = new URLSearchParams(queries).toString()
  const url = `${apiUrl}?${params}`

  const setQuery = (data, isFilter = false, isReset = false) => {
    const newQueries = { ...queries, ...data }
    if (isFilter && data === null || isReset) {
      delete newQueries.status
      setFilter({})
    }
    // if a page wasn't clicked, and anything else was changed reset page to 0.
    if (data && !data.page) {
      newQueries.page = 0
    }
    setQueries(newQueries)
    localStorage.setItem(apiUrl, JSON.stringify(newQueries))
  }

  const { data: transfers, error } = useQuery({ queryKey: [url], refetchInterval: 10000 })


  const filterOptions = [
    {
      label: 'Status',
      key: 'status',
      type: 'checkboxes',
      options: [
        { key: 'transmitting', label: 'Transmitting' },
        { key: 'error', label: 'Error' },
        { key: 'pending', label: 'Pending' },
        { key: 'completed', label: 'Completed' }
      ]
    }
  ]

  useEffect(() => {
    if (checked.length > 0) {
      updateTitle(`Transfers (${checked.length})`)
    }
    else {
      updateTitle('Transfers')
    }
  }, [checked])

  return (
    <Table
      status={{
        pending: !transfers,
        pendingMessage: 'Loading transfers...',
        error: error
      }}
      header={{
        customElement: <ResetTableFilters filterCallback={() => {
          setQuery(headerDefaults, false, true);
        }}
        />,
        columns: [
          { text: '' },
          { text: 'Title' },
          { text: 'Size' },
          { text: 'Site' },
          { text: 'Status' },
          // { text: 'Delivery Date', key: 'deliver_at' },
          // { text: 'Created Date', key: 'created_at' },
          { text: '' }
        ],
        searching: {
          search: queries.search,
          searchPlaceholder: 'Search...',
          onSearch: search => setQuery({ search }),
          rightIcon: {
            icon: 'close',
            onClick: () => setQuery({ search: '' }),
            tooltip: {
              text: "Clear search"
            }
          }
        },
        filtering: {
          options: filterOptions,
          filters: filter,
          onFiltersChange: value => {
            setFilter(value)
            setQuery(value, true)
          }
        },
        sorting: {
          options: [
            { key: 'deliver_at', label: 'Delivery Date' },
            { key: 'created_at', label: 'Created Date' }
          ],
          key: queries.sortBy,
          sortingKey: queries.sortBy,
          direction: queries.sortDirection,
          onSortChange: (sort) => setQuery({ sortBy: sort.key, sortDirection: sort.direction })
        }
        // checkbox: {
        //   checked: checked.length > 0,
        //   indeterminate: transfers?.total && checked.length !== transfers?.total,
        //   onChange: () => {
        //     if (checked.length === 0) {
        //       setChecked(transfers?.data.map(({ id }) => id))
        //     }
        //     else {
        //       setChecked([])
        //     }
        //   }
        // }
      }}
      widths={[45, 'auto', 110, 200, 140, 55]}
      body={{
        data: transfers?.data,
        row: {
          spaced: true,
          onClick: (event, data) => showDelivery(data),
          // checkbox: {
          //   checked: (data, index) => checked.includes(data.id),
          //   onChange: (data, index) => (checked.includes(data.id)
          //     ? setChecked(checked.filter(i => i !== data.id))
          //     : setChecked([...checked, data.id]))
          // },
          render: [
            ({ is_electronic }) => <DeliveryType isElectronic={is_electronic} />,
            ({ version, title }) => <div className='flex flex-col text-xs truncate'>
              <span className='font-semibold'>{title?.friendly_title ?? 'N/A'}</span>
              <span className='truncate text-2xs'>{version?.version_name ?? 'N/A'}</span>
            </div>,
            ({ version }) => <div className='text-xs'>{humanFileSize(version?.size ?? 0)}</div>,
            ({ cinema }) => <div className='text-xs truncate'>{cinema?.name ?? 'N/A'}</div>,
            ({ status, transfer_speed }) => (status ? <div className='uppercase'>
              <Tag
                outline
                label={status}
                type={status.toLowerCase() === 'completed' ? 'success' : 'primary'} />
              <br /> {transfer_speed}
            </div> : null),
            // ({ booking }) => <span className='text-xs'>{convertToLocal(booking?.deliver_at)}</span>,
            // ({ created_at }) => <span className='text-xs'>{convertToLocalDateTime(created_at)}</span>,
            data => (checkPermission('delete-deliveries') ? <ButtonDropdown
              button={{ minimal: true, icon: 'more_vert', type: 'neutral' }}
              dropdown={{
                content: [
                  {
                    text: 'Delete',
                    icon: 'delete_forever',
                    onClick: () => {
                      if (window.confirm('Are you sure you want to delete this delivery?')) {
                        deleteDelivery({ deliveryID: data.id })
                      }
                    }
                  }
                ]
              }} /> : null)
          ]
        },
        empty: {
          title: 'No transfers found!',
          text: 'Refine your filters.',
          icon: 'cloud_sync'
        }
      }}
      paginate={{
        totalRows: transfers?.total,
        currentPage: transfers?.current_page - 1,
        rowsPerPage: transfers?.per_page,
        onPageChange: page => setQuery({ page }),
        onRowsPerPageChange: take => setQuery({ take })
      }}
    />
  )
}

export default TransfersTable

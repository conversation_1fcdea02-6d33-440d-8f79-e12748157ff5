import React, { useEffect, useState } from 'react'
import { convertToLocalDateTime } from '/src/helpers/convert_date'
import { Select } from '@bitcine/cinesend-theme'
import { useSites } from '/src/hooks/sites'

const SiteStatus = ({ site, disabled }) => {
  const [currentStatus, setCurrentStatus] = useState(site?.status)

  const { updateSite } = useSites()
  const saveField = newData => {
    setCurrentStatus(newData.status)
    updateSite({
      siteID: site.id,
      ...newData,
      setErrors: () => {}
    })
  }

  const statuses = [
    { label: 'Pending Installation', value: 'pending' },
    { label: 'Faulted', value: 'fault' },
    { label: 'Online', value: 'online' },
    { label: 'Manually Disabled', value: 'forced' }
  ]

  useEffect(() => {
    setCurrentStatus(site?.status)
  }, [site])

  return (
    <>
      {site &&
        <div className="flex flex-col space-x-4 items-end">
          <Select
            disabled={disabled}
            className={'w-4/5'}
            options={statuses}
            value={statuses.find(status => status.value === currentStatus)}
            onChange={value => {
              saveField({ status: value.value })
            }}
          />
          <small className={'mt-2 text-xs text-gray-400'}>
            Last online: {convertToLocalDateTime(site?.media_manager_status_updated_at)}</small>
        </div>
      }
    </>
  )
}

export default SiteStatus

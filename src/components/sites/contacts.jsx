import React, { useState } from 'react'
import { useSites } from '/src/hooks/sites'
import { Input, Button, Table, Modal, ButtonDropdown } from '@bitcine/cinesend-theme'
import { useAuth } from '/src/hooks/auth'

const Contacts = ({ site }) => {
  const { checkPermission } = useAuth()
  const [modal, setModal] = useState(false)
  const [data, setData] = useState({})
  const { createContact, deleteContact } = useSites()
  return (
    <div className="flex flex-col space-y-4">

      <div className="flex flex-row justify-between items-end">
        <div>&nbsp;</div>
        <Button
          disabled={!checkPermission('update-cinemas')}
          onClick={() => {
            setData({})
            setModal(true)
          }}>New Contact</Button>
      </div>
      <Table
        status={{
          pending: !site,
          pendingMessage: 'Loading Contacts...',
          error: !site
        }}
        header={{
          columns: [
            { text: 'Name', key: 'name' },
            { text: 'Phone', key: 'phone_number' },
            { text: 'Email', key: 'email' },
            { text: 'Role', key: 'role' },
            { text: ''}
          ]
        }}
        widths={['auto', 300, 300, 200, 40]}
        body={{
          data: site?.contacts ?? [],
          row: {
            spaced: true,
            onClick: (event, data) => {
              setData(data)
              setModal(true)
            },
            render: [
              ({ name }) => name,
              ({ phone_number }) => phone_number,
              ({ email }) => email,
              ({ role }) => role,
              data => (checkPermission('update-cinemas') ? <ButtonDropdown
                button={{ minimal: true, icon: 'more_vert', type: 'neutral' }}
                dropdown={{
                  content: [
                    {
                      text: 'Delete',
                      icon: 'delete_forever',
                      onClick: () => {
                        if (window.confirm('Are you sure you want to delete this contact?')) {
                          deleteContact({ contactID: data.id, siteID: site.id })
                        }
                      }
                    }
                  ]
                }}/> : null)
            ]
          },
          empty: {
            title: 'No Contacts Yet.',
            text: '',
            icon: 'contact'
          }
        }}
      />
      {modal ?
        <Modal
          header={`${data.id ? 'Update' : 'Create'} Contact`}
          onClose={() => setModal(false)}
          confirmButton={{
            text: 'Save Contact',
            onClick: () => {
              createContact({ ...data, siteID: site.id })
              setModal(false)
            },
            disabled: !data.name || (!data.phone_number && !data.email)
          }}>
          <div className="flex flex-col space-y-4">
            <Input label={'Name'} value={data.name}
              onChange={event => setData({ ...data, name: event.target.value })}/>
            <Input label={'Phone number'} value={data.phone_number}
              onChange={event => setData({ ...data, phone_number: event.target.value })}/>
            <Input label={'Email'} value={data.email}
              onChange={event => setData({ ...data, email: event.target.value })}/>
            <Input label={'Role'} value={data.role}
              onChange={event => setData({ ...data, role: event.target.value })}/>
          </div>
        </Modal> : null}
    </div>
  )

}

export default Contacts

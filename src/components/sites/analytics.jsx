import { Mo<PERSON>, Status, DatePicker, Button } from '@bitcine/cinesend-theme'
import React, { useEffect, useRef, useState } from 'react'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  BarElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  TimeScale,
  TimeSeriesScale,
  LineController
} from 'chart.js'
import { Bar, Line } from 'react-chartjs-2'
import dayjs from 'dayjs'
import 'chartjs-adapter-dayjs-4/dist/chartjs-adapter-dayjs-4.esm'
import { useQuery, useQueryClient } from '@tanstack/react-query'

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineController,
  LineElement,
  TimeScale,
  TimeSeriesScale,
  Title,
  Tooltip,
  Legend
)

const defaultOptions = {
  interaction: {
    intersect: false,
    mode: 'index'
  },
  responsive: true,
  plugins: {
    legend: {
      position: 'bottom'
    }
  },
  scales: {
    x: {
      min: dayjs().subtract(7, 'day').format(),
      max: dayjs().format(),
      type: 'time',
      display: true,
      time: {
        unit: 'day',
        tooltipFormat: 'MMM DD hh:mm a'
      },
      grid: {
        drawTicks: false,
        offset: false
      },
      ticks: {
        label: 'end'
      }
    },
    y: {
      display: true,
      title: {
        display: true,
        text: 'Speed in Mb/s'
      },
      suggestedMax: 200,
      suggestedMin: 0
    }
  }
}

const Analytics = ({ site }) => {

  const chartRef = useRef()

  const [errors, showErrors] = useState(null)
  const [startDate, setStartDate] = useState(dayjs().subtract(7, 'days'))
  const [endDate, setEndDate] = useState(dayjs())
  const queryClient = useQueryClient()

  const getApiUrl = () =>
    `/api/cinemas/${site.id}/analytics?start=${startDate.format('YYYY-MM-DD')}&end=${endDate.format('YYYY-MM-DD')}`

  const [apiUrl, setApiUrl] = useState(getApiUrl())

  const { data: analytics, isLoading } = useQuery({ queryKey: [apiUrl], enabled: !!apiUrl })
  const mutate  = () => queryClient.invalidateQueries({ queryKey: [apiUrl] })

  useEffect(() => {
    if (analytics && chartRef.current) {
      chartRef.current.options.scales.x.min = dayjs(analytics.start).format()
      chartRef.current.options.scales.x.max = dayjs(analytics.end).format()
      chartRef.current.update()
    }
  }, [analytics])

  const data = {
    datasets: analytics ? [
      {
        label: 'Average Download Speed',
        type: 'line',
        cubicInterpolationMode: 'monotone',
        data: analytics.speeds.map(opt => ({
          x: dayjs(opt.graph_label).format(), // yyyy-day of year-hour
          y: opt.average_speed
        })),
        borderColor: 'rgb(0, 178, 239)',
        backgroundColor: 'rgba(0, 178, 239, 0.5)'
      },
      {
        type: 'bar',
        label: 'Min/Max Download Speed',
        data: analytics.speeds.map(opt => ({
          x: dayjs(opt.graph_label).format(),
          y: [opt.min_speed, opt.max_speed] // ranged
        })),
        borderColor: 'rgb(224,60,60)',
        backgroundColor: 'rgba(252,0,53,0.5)'
      }
    ] : []
  }

  return (
    <div className={'w-full max-w-4xl'}>
      <Status pending={!analytics || isLoading}>
        <div className={'flex flex-row space-x-2 items-end'}>
          <DatePicker
            width={'w-1/3'}
            showJumpToToday={true}
            isClearable={false}
            date={startDate}
            onChange={(newDate) => setStartDate(dayjs(newDate))}
            maxDate={dayjs(endDate)}
            showTimeSelect={false}
            label={'Start Date'}
          />

          <DatePicker
            width={'w-1/3'}
            showJumpToToday={true}
            isClearable={false}
            date={endDate}
            onChange={(newDate) => setEndDate(dayjs(newDate))}
            maxDate={dayjs()}
            minDate={dayjs(startDate)}
            showTimeSelect={false}
            label={'End Date'}
          />
          <Button className={'-left-4 -top-4'} onClick={() => {
            setApiUrl(getApiUrl())
            mutate()
          }}>Apply</Button>
        </div>
        <div className="w-full">
          <Bar
            ref={chartRef}
            options={defaultOptions}
            data={data}/>
        </div>
        {errors && <Modal onClose={() => showErrors(null)} header="Errors">
          <div className="text-xs">{errors}</div>
        </Modal>}
      </Status>
    </div>
  )
}
export default Analytics

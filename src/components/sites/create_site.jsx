import { But<PERSON>, Mo<PERSON> } from '@bitcine/cinesend-theme'
import React, { useState } from 'react'
import { useSites } from '/src/hooks/sites'
import ErrorMessages from '/src/components/error_messages'
import FieldsForm from '/src/components/fields_form'
import FIELDS from '/src/constants/sites/fields'
import { useAuth } from '/src/hooks/auth'

const CreateSite = () => {
  const { checkPermission } = useAuth()

  const [errors, setErrors] = useState([])
  const [open, setOpen] = useState(false)
  const [data, setData] = useState({})
  const [pending, setPending] = useState(false)
  const update = newData => setData({ ...data, ...newData })
  const { createSite } = useSites()
  const submit = () => {
    setPending(true)
    createSite({
      ...data, setErrors: errors => {
        setPending(false)
        setErrors(errors)
      }
    })
  }
  return (
    <>
      { checkPermission('create-cinemas') &&
        <Button icon="add" onClick={() => setOpen(true)}>Create site</Button>}
      {open ?
        <Modal
          header="Create new site"
          onClose={() => setOpen(false)}
          pending={pending}
          confirmButton={{
            text: 'Create site',
            onClick: () => submit(),
            disabled: !data.name || !data.address || !data.zip || !data.city
          }}>
          <FieldsForm
            data={data}
            fields={FIELDS.general.find(group => group.title === 'Location').fields}
            updateState={update}
            saveField={update}/>
          <ErrorMessages errors={errors}/>
        </Modal> : null}
    </>)
}

export default CreateSite

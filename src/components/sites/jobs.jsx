import { ButtonDropdown, Modal, Table } from '@bitcine/cinesend-theme'
import { convertToLocalDateTime } from '/src/helpers/convert_date'
import { useJobs } from '/src/hooks/jobs'
import React, { useState } from 'react'
import { useAuth } from '/src/hooks/auth'
import { useQuery } from '@tanstack/react-query'

const Jobs = ({ site }) => {

  const { retryJob, deleteJob } = useJobs()
  const { checkPermission } = useAuth()

  const [errors, showErrors] = useState(null)

  const apiUrl = `/api/cinemas/${site.id}/jobs`

  const defaults = localStorage.getItem(apiUrl) !== null
    ? JSON.parse(localStorage.getItem(apiUrl))
    : { search: '', page: 0, take: 15, sortBy: 'created_at', direction: 'DESC' }

  const [queries, setQueries] = useState(defaults)

  const params = new URLSearchParams(queries).toString()
  const url = `${apiUrl}?${params}`

  const setQuery = (data) => {
    const newQueries = {...queries, ...data}
    // if a page wasn't clicked, and anything else was changed reset page to 0.
    if (data && !data.page) {
      newQueries.page = 0
    }
    setQueries(newQueries)
    localStorage.setItem(apiUrl, JSON.stringify(newQueries))
  }

  const { data: jobs, error } = useQuery({ queryKey: [url], refetchInterval: 10000 })

  return (
    <>
      <Table
        status={{
          pending: !jobs,
          pendingMessage: 'Loading jobs...',
          error: error
        }}
        header={{
          columns: [
            { text: 'Name', key: 'name' },
            { text: 'Status', key: 'status' },
            { text: 'Progress', key: 'progress' },
            { text: 'Created', key: 'created_at' },
            { text: 'Updated', key: 'updated_at' },
            { text: '' }
          ],
          searching: {
            search: queries.search,
            searchPlaceholder: 'Search...',
            onSearch: search => setQuery({search}),
            rightIcon: {
              icon: 'close',
              onClick: () => setQuery({search: ''}),
              tooltip: {
                text: "Clear search"
              }
            }
          },
          sorting: {
            options: [
              { key: 'Name', label: 'Name' },
              { label: 'Created', key: 'created_at' },
              { label: 'Updated', key: 'updated_at' }
            ],
            key: queries.sortBy,
            sortingKey: queries.sortBy,
            direction: queries.sortDirection,
            onSortChange: (sort) => setQuery({ sortBy: sort.key, sortDirection: sort.direction })
          }
        }}
        widths={['auto', 100, 80, 220, 220, 65]}
        body={{
          data: jobs?.data,
          row: {
            spaced: true,
            // checkbox: {
            //   checked: (data) => addedContent[type].some(content => content._id === data._id),
            //   onChange: (data) => determineChecked(data),
            //   disabled: (data) => isAssetSelected(data._id)
            // },
            render: [
              ({ name }) => <div className='text-xs'>{name}</div>,
              ({ status }) => <div className='capitalize text-xs'>{status}</div>,
              ({ progress }) => <div className='text-xs'>{progress}%</div>,
              ({ created_at }) => <div className='text-xs'>{convertToLocalDateTime(created_at)}</div>,
              ({ updated_at }) => <div className='text-xs'>{convertToLocalDateTime(updated_at)}</div>,
              data => (checkPermission('update-cinemas') ? <ButtonDropdown
                button={{
                  minimal: true, icon: 'more_vert', type: 'neutral'
                }}
                dropdown={{
                  content: [
                    {
                      text: 'Retry Job',
                      icon: 'refresh',
                      onClick: () => retryJob({ jobID: data.id }),
                      show: ['pending', 'failed', 'triggered'].includes(data.status.toLowerCase())
                    },
                    {
                      text: 'Delete Job',
                      icon: 'delete_forever',
                      onClick: () => {
                        if (window.confirm('Are you sure you want to delete this job?')) {
                          deleteJob({ jobID: data.id })
                        }
                      },
                      show: true
                    },
                    {
                      text: 'Show Errors',
                      icon: 'visibility',
                      onClick: () => showErrors(data.errors),
                      show: !!data.errors
                    }
                  ].filter(opt => opt.show)
                }}/> : null)
            ]
          },
          empty: {
            title: 'No jobs found!',
            icon: 'theaters'
          }
        }}
        paginate={{
          totalRows: jobs?.total,
          currentPage: jobs?.current_page - 1,
          rowsPerPage: jobs?.per_page,
          onPageChange: page => setQuery({ page }),
          onRowsPerPageChange: take => setQuery({ take })
        }}
      />
      {errors && <Modal onClose={() => showErrors(null)} header='Errors'>
        <div className='text-xs'>{errors}</div>
      </Modal>}
    </>
  )
}
export default Jobs

import React, { useEffect, useState } from 'react'
import { Table, Icon, ButtonDropdown } from '@bitcine/cinesend-theme'
import { useNavigate } from 'react-router-dom'
import { convertToLocalDateTime, convertSecondsToHoursMinutes } from '/src/helpers/convert_date'
import { useAuth } from '/src/hooks/auth'
import { useSites } from '/src/hooks/sites'
import { useQuery } from '@tanstack/react-query'
import ResetTableFilters from '/src/components/reset_table_filters'
import circuitOptions from '/src/constants/sites/circuits'
import { states } from '/src/constants/sites/states'

const SITES_API = '/api/cinemas'

function SitesTable({ updateTitle = (title) => {} }) {
  const { checkPermission } = useAuth()
  const navigate = useNavigate()
  const [checked, setChecked] = useState([])
  const { deleteSite } = useSites()

  useEffect(() => {
    if (checked.length > 0) {
      updateTitle(`Sites (${checked.length})`)
    } else {
      updateTitle('Sites')
    }
  }, [checked])

  function getIcon(data) {
    return {
      pending: (
        <Icon tooltip={{ text: 'Pending installation' }} icon='schedule' copy={false} className='text-gray-400' />
      ),
      online: <Icon tooltip={{ text: 'Online' }} icon='check' copy={false} className='text-green-600' />,
      fault: (
        <Icon
          tooltip={{
            text: `This site has faulted - last online: ${convertToLocalDateTime(data.media_manager_status_updated_at)}`,
          }}
          icon='error'
          copy={false}
          className='text-error-500'
        />
      ),
      recovered: (
        <Icon
          tooltip={{ text: 'This site has recovered from a fault' }}
          icon='check'
          copy={false}
          className='text-orange-500'
        />
      ),
      forced: (
        <Icon
          tooltip={{ text: 'This site has been set offline.' }}
          icon='cloud_off'
          copy={false}
          className='text-error-500'
        />
      ),
    }[data.status]
  }

  const filterOptions = [
    {
      label: 'Status',
      key: 'status',
      type: 'checkboxes',
      options: [
        { key: 'pending', label: 'Pending Install' },
        { key: 'online', label: 'Online' },
        { key: 'fault', label: 'Faulted' },
        { key: 'forced', label: 'Manually Disabled' },
      ],
    },
    {
      label: 'State',
      key: 'state',
      type: 'checkboxes',
      options: states.map(({ label, value }) => ({ label, key: value })),
    },
    {
      label: 'Circuit',
      key: 'circuit',
      type: 'checkboxes',
      options: circuitOptions,
    },
  ]

  const headerDefaults = { search: '', page: 0, take: 15, sortBy: 'name', direction: 'ASC' }

  const defaults =
    localStorage.getItem(SITES_API) !== null ? JSON.parse(localStorage.getItem(SITES_API)) : headerDefaults

  const [queries, setQueries] = useState(defaults)
  const [filter, setFilter] = useState({})

  const params = new URLSearchParams(queries).toString()
  const url = `${SITES_API}?${params}`

  const setQuery = (data, isFilter = false, isReset = false) => {
    const newQueries = { ...queries, ...data }
    if ((isFilter && data === null) || isReset) {
      delete newQueries.state
      delete newQueries.status
      delete newQueries.circuit
      setFilter({})
    }

    // if a page wasn't clicked, and anything else was changed reset page to 0.
    if (data && !data.page) {
      newQueries.page = 0
    }

    setQueries(newQueries)
    localStorage.setItem(SITES_API, JSON.stringify(newQueries))
  }

  const { data: sites, error } = useQuery({ queryKey: [url] })

  return (
    <div className='flex flex-col space-y-4'>
      <Table
        status={{
          pending: !sites,
          pendingMessage: 'Loading sites...',
          error: error,
        }}
        header={{
          customElement: (
            <ResetTableFilters
              filterCallback={() => {
                setQuery(headerDefaults, false, true)
              }}
            />
          ),
          columns: [
            { text: 'Status' },
            { text: 'Name', key: 'name' },
            { text: 'TCN' },
            { text: 'Secondary TCN' },
            { text: 'Disney Site ID' },
            { text: 'Sony Site ID' },
            { text: 'Rentrack' },
            { text: 'DCDC/Sage' },
            { text: 'Address', key: 'address' },
            { text: 'Circuit', key: 'circuit' },
            { text: 'City', key: 'city' },
            { text: 'State' },
            { text: 'Country' },
            { text: 'Typical Speed' },
          ],
          searching: {
            search: queries.search,
            searchPlaceholder: 'Search...',
            onSearch: (search) => setQuery({ search }),
            rightIcon: {
              icon: 'close',
              onClick: () => setQuery({ search: '' }),
              tooltip: {
                text: 'Clear search',
              },
            },
          },
          filtering: {
            options: filterOptions,
            filters: filter,
            onFiltersChange: (value) => {
              setFilter(value)
              setQuery(value, true)
            },
          },
          sorting: {
            options: [
              { key: 'name', label: 'Name' },
              { key: 'address', label: 'Address' },
              { key: 'circuit', label: 'Circuit' },
              { key: 'city', label: 'City' },
            ],
            key: queries.sortBy,
            sortingKey: queries.sortBy,
            direction: queries.sortDirection,
            onSortChange: (sort) => setQuery({ sortBy: sort.key, sortDirection: sort.direction }),
          },
          // checkbox: {
          //   checked: checked.length > 0,
          //   indeterminate: sites?.total && checked.length !== sites?.total,
          //   onChange: () => {
          //     if (checked.length === 0) {
          //       setChecked(sites?.data.map(({ id }) => id))
          //     }
          //     else {
          //       setChecked([])
          //     }
          //   }
          // }
        }}
        initialColumns={[0, 1, 2, 8, 9, 10, 11, 12, 13, 14]}
        widths={[55, 200, 70, 70, 70, 70, 70, 70, 200, 150, 200, 100, 100, 150, 55]}
        body={{
          data: sites?.data,
          row: {
            spaced: true,
            onClick: checkPermission('view-cinemas') ? (event, data) => navigate(`/sites/${data.id}`) : null,
            // checkbox: {
            //   checked: (data, index) => checked.includes(data.id),
            //   onChange: (data, index) => (checked.includes(data.id)
            //     ? setChecked(checked.filter(i => i !== data.id))
            //     : setChecked([...checked, data.id]))
            // },
            render: [
              (data) => getIcon(data),
              ({ name }) => <div className='font-medium'>{name}</div>,
              ({ tcn }) => tcn,
              ({ secondary_tcn }) => secondary_tcn,
              ({ disney_site_id }) => disney_site_id,
              ({ sony_site_id }) => sony_site_id,
              ({ rentrack }) => rentrack,
              ({ sage_customer_number }) => sage_customer_number,
              ({ address }) => address,
              ({ circuit }) => circuit,
              ({ city }) => city,
              ({ state }) => state,
              ({ country_code }) => country_code,
              ({ typical_speeds }) => (
                <>
                  {typical_speeds?.seconds_to_download_150GB ? (
                    <div className='flex-col flex'>
                      <div className='font-bold'>
                        {convertSecondsToHoursMinutes(typical_speeds.seconds_to_download_150GB)}
                        /150GB
                      </div>
                      <div className='text-xs'>{typical_speeds.download_speed_in_mbps} Mbps avg.</div>
                    </div>
                  ) : (
                    'N/A'
                  )}
                </>
              ),
              (data) =>
                checkPermission('delete-cinemas') ? (
                  <ButtonDropdown
                    button={{
                      minimal: true,
                      icon: 'more_vert',
                      type: 'neutral',
                    }}
                    dropdown={{
                      content: [
                        {
                          text: 'Delete Site',
                          icon: 'delete_forever',
                          onClick: () => {
                            if (window.confirm('Are you sure you want to delete this site?')) {
                              deleteSite({ siteID: data.id })
                            }
                          },
                          show: checkPermission('delete-cinemas'),
                        },
                      ].filter((opt) => opt.show),
                    }}
                  />
                ) : (
                  <></>
                ),
            ],
          },
          empty: {
            title: 'No sites found!',
            text: 'Refine your filters.',
            icon: 'theaters',
          },
        }}
        paginate={{
          totalRows: sites?.total,
          currentPage: sites?.current_page - 1,
          rowsPerPage: sites?.per_page,
          onPageChange: (page) => setQuery({ page }),
          onRowsPerPageChange: (take) => setQuery({ take }),
        }}
      />
    </div>
  )
}

export default SitesTable

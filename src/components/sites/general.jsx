import React, { useState } from 'react'
import { useSites } from '/src/hooks/sites'
import ErrorMessages from '/src/components/error_messages'
import FieldsForm from '/src/components/fields_form'
import FIELDS from '/src/constants/sites/fields'
import NotesPanel from '/src/components/notes/panel'
import { useAuth } from '/src/hooks/auth'

const General = ({ site }) => {

  const {checkPermission} = useAuth()
  const [errors, setErrors] = useState([])
  const [data, setData] = useState(site)
  const updateState = newData => {
    setData({ ...data, ...newData })
  }
  const { updateSite } = useSites()
  const saveField = newData => {
    updateSite({
      siteID: site.id,
      ...newData,
      setErrors: errors => setErrors(errors)
    })
  }
  return (
    <div className='flex flex-col space-y-4'>
      <div className='flex space-x-4'>
        {FIELDS.general.map((group, index) =>
          <FieldsForm
            key={index}
            disabled={!checkPermission('update-cinemas')}
            data={data}
            updateState={updateState}
            saveField={saveField}
            {...group}/>
        )}
      </div>
      <ErrorMessages errors={errors}/>
      <NotesPanel modelName={'site'} modelId={site?.id}/>
    </div>
  )

}

export default General

import React from 'react'
import { Modal } from '@bitcine/cinesend-theme'

function SiteInfoModal({ cinema, onClose }) {
  if (!cinema) return null

  return (
    <Modal
      header={`${cinema?.name} Info`}
      size='small'
      minWidth={false}
      className='w-full max-w-md'
      onClose={onClose}
    >
      <div>
        <p>
          <strong>TCN:</strong> {cinema?.tcn}
        </p>
        <p>
          <strong>Site Name:</strong> {cinema?.name}
        </p>
        <p>
          <strong>Site Address:</strong> {cinema?.address}
        </p>
        <p>
          <strong>Site Circuit:</strong> {cinema?.circuit}
        </p>
      </div>
    </Modal>
  )
}

export default SiteInfoModal

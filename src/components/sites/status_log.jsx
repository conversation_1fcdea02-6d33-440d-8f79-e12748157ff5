import { Table } from '@bitcine/cinesend-theme'
import { convertToLocalDateTime } from '/src/helpers/convert_date'
import { useState } from 'react'

function StatusLog ({ site }) {
  const [inlineSearch, setInlineSearch] = useState('');
  const [filter, setFilter] = useState({});

  const filterOptions = [
    {
      label: 'New State',
      key: 'new_state',
      type: 'radio_buttons',
      options: [
        { key: 'pending', label: 'Pending' },
        { key: 'online', label: 'Online' },
        { key: 'fault', label: 'Fault' },
        { key: 'recovered', label: 'Online, Recovered' }
      ]
    }

  ]

  return (
    <>
      <Table
        status={{
          pending: !site,
          pendingMessage: 'Loading Site...'
        }}
        header={{
          columns: [
            { text: 'Date & Time' },
            { text: 'Previous State' },
            { text: 'New State' },
            { text: 'User' }
          ],
          filtering: {
            options: filterOptions,
            filters: filter,
            onFiltersChange: value => {
              setFilter(value)
            }
          },
          searching: {
            search: inlineSearch,
            searchPlaceholder: 'Search...',
            onSearch: search => setInlineSearch(search),
            rightIcon: {
              icon: 'close',
              onClick: () => setQuery({search: ''}),
              tooltip: {
                text: "Clear search"
              }
            }
          }
        }}
        widths={['auto', 400, 400, 200]}
        body={{
          data: site?.status_log.filter(data => {
            if (inlineSearch || filter) {
              let filterMatch = true
              if (filter && filter['new_state']) {
                filterMatch = (data.new_state === filter.new_state)
              }
              let search = data.old_state.includes(inlineSearch) || data.new_state.includes(inlineSearch)
              return search && filterMatch
            }
            return true
          }),
          row: {
            spaced: true,
            render: [
              ({ updated_at }) => convertToLocalDateTime(updated_at),
              ({ old_state }) => old_state,
              ({ new_state }) => new_state,
              ({ user_name }) => user_name
            ]
          },
          empty: {
            title: 'No Statuses Changed.',
            text: '',
            icon: 'theaters'
          }
        }}
      />
    </>
  )
}

export default StatusLog

import React, { useState } from 'react'
import { Table } from '@bitcine/cinesend-theme'
import { useNavigate } from 'react-router-dom'
import ResetTableFilters from '/src/components/reset_table_filters'
import { useQuery } from '@tanstack/react-query'

const SITES_API = '/api/cinemas'

function MiniSitesTable ( { onClick }) {

  const navigate = useNavigate()

  const headerDefaults = { search: '', page: 0, take: 15, sortBy: 'name', direction: 'ASC' }

  const defaults = localStorage.getItem(SITES_API) !== null
    ? JSON.parse(localStorage.getItem(SITES_API))
    : headerDefaults

  const [queries, setQueries] = useState(defaults)

  const params = new URLSearchParams(queries).toString()
  const url = `${SITES_API}?${params}`

  const setQuery = (data) => {
    const newQueries = {...queries, ...data}
    // if a page wasn't clicked, and anything else was changed reset page to 0.
    if (data && !data.page) {
      newQueries.page = 0
    }
    setQueries(newQueries)
    localStorage.setItem(SITES_API, JSON.stringify(newQueries))
  }

  const { data: sites, error } = useQuery({ queryKey: [url] })

  return (
    <div className='flex flex-col space-y-2'>
      <Table
        status={{
          pending: !sites,
          pendingMessage: 'Loading sites...',
          error: error
        }}
        header={{
          customElement: <ResetTableFilters filterCallback={() => {
            setQuery(headerDefaults, false, true);
          }}
          />,
          columns: [
            { text: 'Name', key: 'name' },
            { text: 'Address', key: 'address' },
            { text: 'Circuit', key: 'circuit' }
          ],
          searching: {
            search: queries.search,
            searchPlaceholder: 'Search...',
            onSearch: search => setQuery({ search }),
            rightIcon: {
              icon: 'close',
              onClick: () => setQuery( { search: '' }),
              tooltip: {
                text: "Clear search"
              }
            }
          },
          sorting: {
            options: [
              { key: 'name', label: 'Name' },
              { key: 'address', label: 'Address'},
              { key: 'circuit', label: 'Circuit' }
            ],
            key: queries.sortBy,
            sortingKey: queries.sortBy,
            direction: queries.sortDirection,
            onSortChange: (sort) => setQuery({ sortBy: sort.key, sortDirection: sort.direction })
          }
        }}
        widths={[81, 81, 81]}
        body={{
          data: sites?.data,
          row: {
            spaced: true,
            // checkbox: {
            //   checked: (data) => addedContent[type].some(content => content._id === data._id),
            //   onChange: (data) => determineChecked(data),
            //   disabled: (data) => isAssetSelected(data._id)
            // },
            onClick: (event, data) => onClick(data),
            render: [
              ({ name }) => name,
              ({ address, city, state, country_code }) => <>{address}<br/>{city}, {state}, {country_code}</>,
              ({ circuit }) => circuit
            ]
          },
          empty: {
            title: 'No sites found!',
            text: 'Refine your filters.',
            icon: 'theaters'
          }
        }}
        paginate={{
          totalRows: sites?.total,
          currentPage: sites?.current_page - 1,
          rowsPerPage: sites?.per_page,
          onPageChange: page => setQuery({ page }),
          onRowsPerPageChange: take => setQuery({ take })
        }}
      />
    </div>
  )
}

export default MiniSitesTable

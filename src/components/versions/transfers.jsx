import React, { useEffect, useState } from 'react'
import { withAspera } from '/src/hooks/with_aspera'
import { VersionsContext } from '/src/contexts/versions'

const Transfers = () => {

  const { AW4 } = window
  const { asperaWeb } = withAspera()

  const [time, setTime] = useState(Date.now())
  const [asperaInitialized, setAsperaInitialized] = useState(false)
  const [, dispatch] = React.useContext(VersionsContext)

  useEffect(() => {
    const interval = setInterval(() => setTime(Date.now()), 1000)
    return () => {
      clearInterval(interval)
    }
  }, [])

  useEffect(() => {
    if (!asperaInitialized) {
      setAsperaInitialized(asperaWeb.getStatus() === AW4.Connect.STATUS.RUNNING)
    } else {
      // when transfers changes, update state.versions based on matches.
      asperaWeb.getAllTransfers().then((response) => dispatch({ transfers: response?.transfers}))
    }
  }, [time, asperaInitialized, asperaWeb, AW4.Connect.STATUS.RUNNING, dispatch])

  return (
    <></>
  )
}

export default Transfers

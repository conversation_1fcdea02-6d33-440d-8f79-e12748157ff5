import { useState } from 'react'
import { But<PERSON> } from '@bitcine/cinesend-theme'
import { useTitles } from '/src/hooks/titles'

function CreateVersion ({ title }) {

  const [pending, setPending] = useState(false)

  const { createVersion } = useTitles()
  const create = () => {
    setPending(true)
    createVersion({
      titleID: title.id,
      version_name: 'Pending',
      setErrors: errors => {
        setPending(false)
      }
    })
  }

  return (
    <>
      <Button icon='add' disabled={pending} onClick={() => create()}>Create version</Button>
    </>
  )
}

export default CreateVersion

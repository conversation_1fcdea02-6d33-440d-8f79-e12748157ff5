import { useParams } from 'react-router-dom'
import { Status } from '@bitcine/cinesend-theme'
import Details from './details'
import { useQuery } from '@tanstack/react-query'

const Version = () => {
  
  const { versionID } = useParams()

  const apiUrl = `/api/versions/${versionID}`

  const { data, isLoading } = useQuery({ queryKey: [apiUrl] })
  const version = data?.data

  return (
    <Status pending={isLoading}>
      <Details version={version}/>
    </Status>
  )

}

export default Version

import React, { useState } from 'react'
import { Button, Input } from '@bitcine/cinesend-theme'
import { useTitles } from '/src/hooks/titles'
import ErrorMessages from '/src/components/error_messages'
import getUpdatedData from '/src/helpers/get_updated_data'
import Breadcrumbs from '/src/components/layouts/breadcrumbs'
import AsperaDownload from '/src/components/aspera/aspera_download'
import { useAuth } from '/src/hooks/auth'
import VersionFilesTable from './file_table'

const Details = ({ version }) => {
  const [errors, setErrors] = useState([])
  const [data, setData] = useState(version)
  const [pending, setPending] = useState(false)
  const { checkPermission } = useAuth()
  const update = newData => {
    setData({ ...data, ...newData })
  }
  const { updateVersion, deleteVersion } = useTitles()
  const save = () => {
    setPending(true)
    updateVersion({
      versionID: version.id, ...getUpdatedData(version, data),
      onComplete: () => setPending(false),
      setErrors: errors => {
        setPending(false)
        setErrors(errors)
      }
    })
  }
  return (
    <div className="flex flex-col space-y-4">
      <Breadcrumbs
        breadcrumbs={[
          { to: `/titles/${version.title_id}/versions`, text: 'Versions' },
          { text: version.version_name ?? 'N/A' }]}/>
      <Input
        label="Version Name"
        value={data.version_name}
        onChange={e => update({ version_name: e.target.value })}
      />
      <Input
        label="CPL UUID"
        value={data.cpl_uuid}
        disabled
      />
      <div className="flex justify-between">
        {checkPermission('delete-versions') ?
          <Button
            onClick={() => {
              if (window.confirm('Are you sure you want to permanently delete version?')) {
                deleteVersion({ setErrors, versionID: data.id })
              }
            }}
            type={'error'}>
            Delete Version</Button> : <>&nbsp;</>}
        <Button
          onClick={save}
          disabled={JSON.stringify(data) === JSON.stringify(version) || pending}>
          Save
        </Button>
      </div>
      <ErrorMessages errors={errors}/>
      <VersionFilesTable version={version}></VersionFilesTable>
      <div className="flex justify-end"><AsperaDownload version={version}/></div>
    </div>
  )

}

export default Details

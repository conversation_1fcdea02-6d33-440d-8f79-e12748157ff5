import { Table } from '@bitcine/cinesend-theme'
import humanFileSize from '/src/helpers/human_file_size'

function VersionFilesTable ({ version }) {

  return (
    <div className='border-t pt-4'>
      <div className='font-bold'>Files</div>
      <Table
        status={{
          pending: false,
          pendingMessage: '',
          error: false
        }}
        header={{
          columns: [
            { text: 'File Name' },
            { text: 'Size' }
          ]
        }}
        widths={['auto', 200]}
        body={{
          data: version.s3_details?.files ?? [],
          row: {
            spaced: true,
            render: [
              ({ path }) => path.replace(version.s3_details.root, ''),
              ({ size }) => humanFileSize(size)
            ]
          },
          empty: {
            title: 'No files',
            text: '',
            icon: 'file'
          }
        }}
      />
    </div>
  )
}

export default VersionFilesTable

import React, { useContext, useEffect, useState } from 'react'
import { Table, Status } from '@bitcine/cinesend-theme'
import { useNavigate, useParams } from 'react-router-dom'
import AsperaUpload from '/src/components/aspera/aspera_upload'
import { convertToLocalDateTime } from '/src/helpers/convert_date'
import humanFileSize from '/src/helpers/human_file_size'
import { VersionsContext } from '/src/contexts/versions'
import Orphans from '/src/components/titles/orphans'
import { useQuery } from '@tanstack/react-query'

const VERSIONS_API = '/api/titles/:titleID/versions'

function VersionsTable ({ title }) {

  const { titleID } = useParams()

  const defaults = localStorage.getItem(VERSIONS_API) !== null
    ? JSON.parse(localStorage.getItem(VERSIONS_API))
    : { search: '', page: 0, take: 15, sortBy: 'created_at', sortDirection: 'DESC' }

  const [queries, setQueries] = useState(defaults)

  const params = new URLSearchParams(queries).toString()
  const url = `${VERSIONS_API.replace(':titleID', titleID)}?${params}`

  const setQuery = (data) => {
    const newQueries = { ...queries, ...data }
    // if a page wasn't clicked, and anything else was changed reset page to 0.
    if (data && !data.page) {
      newQueries.page = 0
    }
    setQueries(newQueries)
    localStorage.setItem(url, JSON.stringify(newQueries))
  }

  const [state, dispatch] = useContext(VersionsContext)

  const { data: versions, isFetching, error } = useQuery({ queryKey: [url] })
  const navigate = useNavigate()

  useEffect(() => {
    dispatch({isFetching, versions, error})
  }, [isFetching, dispatch, error, versions])

  return (
    <Status pending={!versions}>
      <Table
        customElement={[<AsperaUpload title={title}/>]}
        status={{
          pending: !versions?.data,
          pendingMessage: 'Loading Content...',
          error: error
        }}
        header={{
          columns: [
            { text: 'Name', key: 'version_name' },
            { text: 'Size', key: 'size' },
            { text: 'Status' },
            { text: 'Created', key: 'created_at' }
          ],
          searching: {
            search: queries.search,
            searchPlaceholder: 'Search...',
            onSearch: search => setQuery({ search }),
            rightIcon: {
              icon: 'close',
              onClick: () => setQuery({search: ''}),
              tooltip: {
                text: "Clear search"
              }
            }
          },
          sorting: {
            options: [
              { key: 'version_name', label: 'Name' },
              { key: 'size', label: 'Size' },
              { key: 'created_at', label: 'Created Date' }
            ],
            key: queries.sortBy,
            sortingKey: queries.sortBy,
            direction: queries.sortDirection,
            onSortChange: (sort) => setQuery({ sortBy: sort.key, sortDirection: sort.direction })
          }
        }}
        widths={['auto', 100, 100, 220]}
        body={{
          data: state.versions ?? [],
          row: {
            spaced: true,
            // checkbox: {
            //   checked: (data) => addedContent[type].some(content => content._id === data._id),
            //   onChange: (data) => determineChecked(data),
            //   disabled: (data) => isAssetSelected(data._id)
            // },
            onClick: (event, data) => navigate(`/titles/${title.id}/content/${data.id}`),
            render: [
              version => <div>
                <div>{getName(version)}</div>
                <div className='text-2xs text-gray-600'>{version.cpl_uuid}</div>
              </div>,
              ({ size }) => humanFileSize(size),
              ({ aspera_transfer, is_ready }) =>
                <div className="flex items-center space-x-1">
                  <span className="capitalize">
                    {is_ready ? 'Ready' : (aspera_transfer ?
                      aspera_transfer.status.replace(/_/g, ' ') : 'Pending Upload')}
                  </span>
                  {aspera_transfer?.aspera_statistics?.bytes_written && aspera_transfer?.status !== 'completed' ?
                    <span>
                        ({parseInt(aspera_transfer.aspera_statistics.bytes_written
                      / aspera_transfer.aspera_statistics.bytes_expected * 100)}%)
                    </span> : null
                  }
                </div>,
              ({ created_at }) => convertToLocalDateTime(created_at)
            ]
          },
          empty: {
            title: 'No DCPs have been uploaded yet.',
            text: 'Upload a DCP to get started.',
            icon: 'movie'
          }
        }}
        paginate={{
          totalRows: versions?.total,
          currentPage: versions?.current_page - 1,
          rowsPerPage: versions?.per_page,
          onPageChange: page => setQuery({ page }),
          onRowsPerPageChange: take => setQuery({ take })
        }}
      />
      <Orphans title={title}/>
    </Status>
  )
}

const getName = version => {
  if (version.version_name) {
    return version.version_name
  }
  try {
    return version.aspera_transfer.transfer_spec.paths[0].source.split('/').slice(-1).pop()
  }
  catch {
    return 'Pending upload...'
  }
}

export default VersionsTable

import { Status, Select, Button, Modal, Input, FileUpload } from '@bitcine/cinesend-theme'
import React, { useState } from 'react'
import { useParams } from 'react-router-dom'
import { useTitles } from '/src/hooks/titles'
import DashboardRow from '/src/components/dashboards/dashboard_row'
import TableCard from '/src/components/dashboards/table_card'
import axios from '/src/lib/axios'
import { useQuery } from '@tanstack/react-query'

const DistByTitle = ({ onSubmit }) => {
  const [titleID, setTitleID] = useState(0)

  const titlesUrl = '/api/titles?take=200'
  const { data: titles } = useQuery({ queryKey: [titlesUrl] })

  const titleOptions = titles ? titles.data.map(title => ({ label: title.friendly_title, value: title.id })) : []

  return (
    <>
      <h4>Distribution By Title Options</h4>

      <Select
        label='Select a Title'
        options={titleOptions}
        value={titleOptions.find(opt => opt.value === titleID)}
        onChange={opt => setTitleID(opt.value)}/>
      <Button
        disabled={!titleID}
        onClick={() => {
          axios.post('api/reports', {
            title_id: titleID,
            type: 'dist_by_title'
          }).then(res => {
            setTitleID(0)
          }).catch(error => {}).finally(() => {
          }).finally(() => {
            onSubmit()
          })
        }}
      >Generate Report</Button>
    </>
  )
}

export default DistByTitle

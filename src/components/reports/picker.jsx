import { Status, Select, Button, Modal, Input, FileUpload } from '@bitcine/cinesend-theme'
import React, { useEffect, useMemo, useState } from 'react'
import { useParams } from 'react-router-dom'
import { useTitles } from '/src/hooks/titles'
import DashboardRow from '/src/components/dashboards/dashboard_row'
import TableCard from '/src/components/dashboards/table_card'
import DistByTitle from './generators/dist_by_title'

const Picker = ({ onSubmit }) => {
  const [selected, setSelected] = useState(0)
  const [active, setActive] = useState(<></>)

  const options = useMemo(() => ([
    { value: 1, key: 'dist_by_title', label: 'Distribution by Title', element: <DistByTitle onSubmit={onSubmit}/> }
  ]), [onSubmit])

  useEffect(() => {
    setActive((options.find(o => o.value === selected)?.element ?? <></>))
  }, [options, selected])

  return (
    <div className={'flex flex-col space-y-4 w-2/5'}>
      <Select options={options}
        label={'Select a Report'}
        value={options.find(opt => opt.value === selected)}
        onChange={e => setSelected(e.value)}
      />
      {active}
    </div>
  )
}

export default Picker

import { Status, Icon, Button, Modal, Input, FileUpload, Table } from '@bitcine/cinesend-theme'
import React, { useState } from 'react'
import { useParams } from 'react-router-dom'
import { useTitles } from '/src/hooks/titles'
import DashboardRow from '/src/components/dashboards/dashboard_row'
import TableCard from '/src/components/dashboards/table_card'
import { convertToShorthand } from '../../helpers/convert_date'
import axios from '../../lib/axios'

const Download = ({downloads}) => {
  // make a hit to download or delete
  return (
    <>
      <h4>Download a Report</h4>
      <Status pending={!downloads}>
        <Table
          status={{
            pending: !downloads
          }}
          header={{
            columns: [
              { text: 'Created'},
              { text: 'Name' },
              { text: '' }
            ]
          }}
          widths={[120,'auto', 200]}
          body={{
            data: downloads ?? [],
            row: {
              spaced: true,
              render: [
                ({ created_at }) => convertToShorthand(created_at),
                ({ title }) => title,
                ({ id, is_processing, is_ready, is_error}) =>
                  <div>
                    {is_ready && !is_error && <Icon icon='download' fontSize='32px'
                      tooltip={{ text: 'Download this report.' }}
                      onClick={() => {
                        axios.get(`api/reports/${id}`).then(res => window.location = res.data.url)
                      }}/> }
                    {is_processing && !is_ready && <Status pending={true}/> }
                    {!is_processing && !is_ready && <Icon icon='timer' fontSize='32px'
                      tooltip={{ text: 'Report is Queued' }}/> }
                    {is_ready && is_error && <Icon icon='warning' fontSize='32px'
                      tooltip={{ text: 'There was an error generating this report. Please try again.' }}/>}
                  </div>
              ]
            },
            empty: {
              title: 'No reports generated.',
              text: 'Pick a report from the list to get started.',
              icon: 'description'
            }
          }}
        />
      </Status>
    </>
  )
}

export default Download

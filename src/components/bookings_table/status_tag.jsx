function StatusTag ({ overall_status }) {

  const color = (status) => ({
    'booked': 'bg-yellow-100 text-yellow-600 border-yellow-200',
    'pending': 'bg-yellow-100 text-yellow-600 border-yellow-200',
    'rejected': 'bg-warning-100 text-warning-600 border-warning-200',
    'transmitting': 'bg-blue-100 text-blue-600 border-blue-200',
    'completed': 'bg-green-100 text-green-600 border-green-200',
    'incomplete': 'bg-red-100 text-red-600 border-red-200',
    'cancelled_after_completion': 'bg-gray-100 text-gray-600 border-gray-200'
  }[status] ?? 'bg-yellow-100 text-yellow-600 border-yellow-200') // default yellow.

  return (
    <div className={`items-center justify-center rounded-xl h-full uppercase text-xs px-2 border
      ${color(overall_status)}`}>
      {overall_status.replaceAll('_', ' ')}
    </div>
  )
}

export default StatusTag

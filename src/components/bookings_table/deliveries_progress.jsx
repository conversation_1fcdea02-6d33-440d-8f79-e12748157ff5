import { convertToFromNow } from '/src/helpers/convert_date'
import humanFileSize from '/src/helpers/human_file_size'

export default function DeliveriesProgress ({ progress }) {
  return (
    <div className="text-xs">
      <div className="font-medium">
        {progress?.transfers_completed ?? 0} / {progress?.transfers_total ?? 0}
      </div>
      {progress?.speed_in_mbps && <div className="font-light">
        <div>{humanFileSize(progress.current_bytes)} / {humanFileSize(
          progress.total_bytes)} • {progress.speed_in_mbps} mbps
        </div>
        <div>ETA: {convertToFromNow(progress.estimated_completion)}</div>
      </div>}
    </div>
  )
}

import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import { Table, ButtonDropdown, Icon } from '@bitcine/cinesend-theme'
import { convertToShorthand, convertToShorthandWithTime } from '/src/helpers/convert_date'
import { useBookings } from '/src/hooks/bookings'
import { useNavigate } from 'react-router-dom'
import DeliveryType from '/src/components/icons/delivery_type'
import { useAuth } from '/src/hooks/auth'
import BookingTitle from '/src/components/bookings/booking_title'
import BookingDescription from '/src/components/bookings/booking_description'
import DeliveriesProgress from './deliveries_progress'
import StatusTag from './status_tag'
import { useQuery } from '@tanstack/react-query'
import ResetTableFilters from '/src/components/reset_table_filters'
import circuitOptions from '/src/constants/sites/circuits'
import SiteInfoModal from '/src/components/sites/site_info_modal'

function BookingsTable({ apiUrl, initialColumns = null, updateTitle = (title) => { } }) {
  const { checkPermission } = useAuth()

  const { deleteBooking } = useBookings()
  const navigate = useNavigate()

  const headerDefaults = {
    search: '',
    page: 0,
    take: 15,
    sortBy: 'created_at',
    sortDirection: 'DESC',
    statusPriority: 'on',
  }

  const defaults = localStorage.getItem(apiUrl) !== null ? JSON.parse(localStorage.getItem(apiUrl)) : headerDefaults

  const [queries, setQueries] = useState(defaults)
  const [modalVisible, setModalVisible] = useState(null)
  const [checked, setChecked] = useState([])
  const [filter, setFilter] = useState({
    status: defaults.status,
    studio: defaults.studio,
    statusPriority: defaults.statusPriority,
  })

  const params = new URLSearchParams(queries).toString()
  const url = `${apiUrl}?${params}`

  // fetch org list filtered by studio
  const orgUrl = '/api/organizations'
  const { data } = useQuery({ queryKey: [orgUrl] })
  const studioList = data?.data.map((org) => ({ key: org.id, label: org.name }))

  // process studio to key label objs for dynamic filter
  const studioOptions = studioList ?? [
    { key: '2', label: 'Disney' },
    { key: '8', label: 'Paramount' },
    { key: '4', label: 'Sony' },
  ]

  const filterOptions = [
    {
      label: 'Status',
      key: 'status',
      type: 'checkboxes',
      options: [
        { key: 'booked', label: 'Booked' },
        { key: 'in_progress', label: 'In Progress' },
        { key: 'completed', label: 'Completed' },
        { key: 'issues', label: 'Issues' },
      ],
    },
    {
      label: 'Studio',
      key: 'studio',
      type: 'checkboxes',
      options: studioOptions,
    },
    {
      label: 'Circuit',
      key: 'circuit',
      type: 'checkboxes',
      options: circuitOptions,
    },
    {
      label: 'In Progress',
      key: 'statusPriority',
      type: 'radio_buttons',
      options: [
        { key: 'on', label: 'Show at top (default)' },
        { key: 'off', label: 'Do not show at top' },
      ],
    },
  ]

  const setQuery = (data, isFilter = false, isReset = false) => {
    const newQueries = { ...queries, ...data }
    if ((isFilter && data === null) || isReset) {
      delete newQueries.status
      delete newQueries.studio
      newQueries.statusPriority = 'on'
      setFilter({ statusPriority: newQueries.statusPriority })
    }
    // if a page wasn't clicked, and anything else was changed reset page to 0.
    if (data && !data.page) {
      newQueries.page = 0
    }
    setQueries(newQueries)
    localStorage.setItem(apiUrl, JSON.stringify(newQueries))
  }

  const { data: bookings, isLoading, error } = useQuery({ queryKey: [url], refetchInterval: 10000 })

  useEffect(() => {
    if (checked.length > 0) {
      updateTitle(`Bookings (${checked.length})`)
    } else {
      updateTitle('Bookings')
    }
  }, [checked])

  console.log(`MY Modal Visible: ${modalVisible}`)
  return (
    <Table
      status={{
        pending: !bookings || isLoading,
        pendingMessage: 'Loading bookings...',
        error: error,
      }}
      initialColumns={initialColumns ? initialColumns : [0, 1, 2, 3, 4, 7, 8, 9, 10, 11, 12]}
      header={{
        customElement: (
          <ResetTableFilters
            filterCallback={() => {
              setQuery(headerDefaults, false, true)
            }}
          />
        ),
        columns: [
          { text: 'Type' },
          { text: 'Title' },
          { text: 'Site', key: 'site' },
          { text: 'Circuit', key: 'circuit' },
          { text: 'Studio' },
          { text: 'Disney ID' },
          { text: 'TCN' },
          { text: 'Status' },
          { text: 'Transfers' },
          { text: 'Engagement Date', key: 'release_date' },
          { text: 'Delivery Deadline', key: 'deliver_at' },
          { text: 'Booking Received', key: 'created_at' },
          { text: '' },
        ],
        filtering: {
          options: filterOptions,
          filters: filter,
          onFiltersChange: (value) => {
            setFilter(value)
            setQuery(value, true)
          },
        },
        searching: {
          search: queries.search,
          searchPlaceholder: 'Search...',
          onSearch: (search) => setQuery({ search }),
          rightIcon: {
            icon: 'close',
            onClick: () => setQuery({ search: '' }),
            tooltip: {
              text: 'Clear search',
            },
          },
        },
        sorting: {
          options: [
            { key: 'site', label: 'Site' },
            { key: 'circuit', label: 'Circuit' },
            { key: 'release_date', label: 'Release Date' },
            { key: 'deliver_at', label: 'Delivery Deadline' },
            { key: 'created_at', label: 'Booking Received' },
          ],
          key: queries.sortBy,
          sortingKey: queries.sortBy,
          direction: queries.sortDirection,
          onSortChange: (sort) => setQuery({ sortBy: sort.key, sortDirection: sort.direction }),
        },
        // checkbox: {
        //   checked: checked.length > 0,
        //   indeterminate: bookings?.total && checked.length !== bookings?.total,
        //   onChange: () => {
        //     if (checked.length === 0) {
        //       setChecked(bookings?.data.map(({ id }) => id))
        //     }
        //     else {
        //       setChecked([])
        //     }
        //   }
        // }
      }}
      widths={[45, 200, 200, 120, 120, 120, 120, 150, 100, 100, 120, 55]}
      body={{
        data: bookings?.data,
        row: {
          spaced: true,
          modalVisible: modalVisible,
          setModalVisible: setModalVisible,
          onClick: checkPermission('view-bookings') ? (event, data) => navigate(`/bookings/${data.id}`) : null,
          // checkbox: {
          //   checked: (data, index) => checked.includes(data.id),
          //   onChange: (data, index) => (checked.includes(data.id)
          //     ? setChecked(checked.filter(i => i !== data.id))
          //     : setChecked([...checked, data.id]))
          // },
          render: [
            ({ is_electronic }) => <DeliveryType isElectronic={is_electronic} />,
            (booking) => (
              <div className='text-xs font-medium truncate'>
                <BookingTitle booking={booking} />
                <br />
                <BookingDescription booking={booking} />
              </div>
            ),
            ({ cinema }, index, { modalVisible, setModalVisible }) => (
              <div className='flex items-center'>
                <span className='text-xs mr-1'>{cinema?.name ?? 'Unmatched Cinema'}</span>
                <Icon
                  icon='info'
                  //size='text-3xl'
                  className='text-lg'
                  onClick={(e) => {
                    console.log(`Clicked on ${cinema?.name}`)
                    setModalVisible(cinema?.id)
                    e.stopPropagation()
                  }}
                />
                {modalVisible === cinema?.id && (
                  <SiteInfoModal
                    cinema={cinema}
                    onClose={() => setModalVisible(null)}
                  />
                )}
              </div>
            ),
            ({ cinema }) => <span className='text-xs'>{cinema?.circuit}</span>,
            ({ organization }) => <span className='text-xs'>{organization?.name}</span>,
            ({ cinema }) => <span className='text-xs'>{cinema?.tcn}</span>,
            ({ cinema }) => <span className='text-xs'>{cinema?.disney_site_id}</span>,
            ({ overall_status }) => (overall_status ? <StatusTag overall_status={overall_status} /> : null),
            ({ transfer_counts }) => <DeliveriesProgress progress={transfer_counts ?? []} />,
            ({ release_date }) => (
              <span className='text-xs'>
                {release_date ? convertToShorthand(dayjs(release_date).tz('GMT')) : 'N/A'}
              </span>
            ),
            ({ deliver_at }) => <span className='text-xs'>{convertToShorthand(deliver_at)}</span>,
            ({ created_at }) => <span className='text-xs'>{convertToShorthandWithTime(created_at)}</span>,
            (data) =>
              checkPermission('delete-bookings') ? (
                <ButtonDropdown
                  button={{ minimal: true, icon: 'more_vert', type: 'neutral' }}
                  dropdown={{
                    content: [
                      {
                        text: 'Delete',
                        icon: 'delete_forever',
                        onClick: () => {
                          if (window.confirm('Are you sure you want to delete this booking?')) {
                            deleteBooking({ bookingID: data.id })
                          }
                        },
                      },
                    ],
                  }}
                />
              ) : null,
          ],
        },
        empty: {
          title: 'No bookings found.',
          text: 'There are no bookings for this title or none that match your filters.',
          icon: 'cloud_sync',
        },
      }}
      paginate={{
        totalRows: bookings?.total,
        currentPage: bookings?.current_page - 1,
        rowsPerPage: bookings?.per_page,
        onPageChange: (page) => setQuery({ page }),
        onRowsPerPageChange: (take) => setQuery({ take }),
      }}
    />
  )
}

export default BookingsTable

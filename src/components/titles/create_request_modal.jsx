import axios from '/src/lib/axios'
import { DatePicker, Modal, Select, Toggle } from '@bitcine/cinesend-theme'
import { useState } from 'react'
import ErrorMessages from '/src/components/error_messages'

const CreateRequestModal = ({ title, onClose }) => {

  const [errors, setErrors] = useState([])
  const [expiresAt, setExpiresAt] = useState(null)
  const [pending, setPending] = useState(false)

  const requestsUrl = `/api/titles/${title.id}/requests`

  const create = () => {
    setPending(true)
    setPending(true)
    axios.post(requestsUrl, {
      expires_at: expiresAt
    }).then(res => {
    }).catch(error => {}).finally(() => {
      setPending(false)
      onClose()
    })
  }

  return (
    <Modal
      header='Create new request'
      onClose={onClose}
      confirmButton={{
        text: 'Create request',
        disabled: pending,
        onClick: () => create()
      }}>
      <div className='flex flex-col space-y-4'>
        <DatePicker
          label='Expires At'
          date={expiresAt}
          showTimeSelect={false}
          onChange={date => setExpiresAt(date)}/>
        <ErrorMessages errors={errors}/>
      </div>
    </Modal>
  )
}

export default CreateRequestModal

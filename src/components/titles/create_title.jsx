import { Button, Modal, Input } from '@bitcine/cinesend-theme'
import React, { useState } from 'react'
import { useTitles } from '/src/hooks/titles'
import ErrorMessages from '/src/components/error_messages'
import { useAuth } from '/src/hooks/auth'
import SelectOrganization from '/src/components/organizations/select_organization'

const CreateTitle = () => {
  const { checkPermission } = useAuth()

  const [errors, setErrors] = useState([])
  const [open, setOpen] = useState(false)
  const [friendlyTitle, setFriendlyTitle] = useState('')
  const [organizationID, setOrganizationID] = useState(0)
  const [pending, setPending] = useState(false)
  const { createTitle } = useTitles()
  const submit = () => {
    setPending(true)
    createTitle({
      friendly_title: friendlyTitle,
      organization_id: organizationID,
      setErrors: errors => {
        setPending(false)
        setErrors(errors)
      }
    })
  }
  return (
    <>
      {checkPermission('create-titles') &&
        <Button icon="add" onClick={() => setOpen(true)}>Create title</Button>}
      {open ?
        <Modal
          header="Create new title"
          onClose={() => setOpen(false)}
          pending={pending}
          confirmButton={{
            text: 'Create title',
            onClick: () => submit(),
            disabled: !friendlyTitle
          }}>
          <div className="flex flex-col space-y-4">
            <Input
              placeholder="The Wizard of Oz"
              label="Title"
              value={friendlyTitle}
              onChange={e => setFriendlyTitle(e.target.value)}
            />
            <SelectOrganization
              label='Studio'
              value={organizationID}
              onChange={organizationID => setOrganizationID(organizationID)}/>
            <ErrorMessages errors={errors}/>
          </div>
        </Modal> : null}
    </>
  )
}

export default CreateTitle

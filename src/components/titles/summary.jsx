import React from 'react'
import { useNavigate } from 'react-router-dom'
import DashboardRow from '/src/components/dashboards/dashboard_row'
import RowCard from '/src/components/dashboards/row_card'
import { Icon } from '@bitcine/cinesend-theme'
import ReleaseTableIndex from '/src/components/releases/release_min_table'

function Summary ({ title }) {
  const navigate = useNavigate()

  const destinations = {
    bookings: {
      url: `/titles/${title.id}/bookings`,
      storage: `/api/titles/${title.id}/bookings`
    },
    release: {
      url: `/titles/${title.id}/releases/:releaseID`,
      storage: `/api/titles/${title.id}/releases`
    }
  }

  const setAndGo = (destination, filters) => {
    // set the local storage for the predefined filter and go to that
    // primary list.
    let url = destinations[destination].url
    let storage = destinations[destination].storage
    localStorage.setItem(storage, JSON.stringify(filters))
    navigate(url)
  }

  const loadingIcon = <Icon icon="refresh" className="animate-spin-slow"/>

  return (
    <>
      {title?.booking_counts &&
        <div className="flex flex-col w-full max-w-4xl">
          <DashboardRow
            rowTitle="Bookings Status">
            <RowCard name="Booked"
              className="cursor-pointer"
              onClick={() => setAndGo('bookings', { status: 'pending,accepted,acknowledged' })}
              value={title?.booking_counts['pending'] ?? loadingIcon} colorCode="text-blue-600"/>
            <RowCard name="Pending"
              className="cursor-pointer"
              onClick={() => setAndGo('bookings', { status: 'transmitting' })}
              value={title?.booking_counts['in_progress'] ?? loadingIcon} colorCode="text-yellow-600"/>
            <RowCard name="Completed"
              className="cursor-pointer"
              onClick={() => setAndGo('bookings', { status: 'completed' })}
              value={title?.booking_counts['completed'] ?? loadingIcon} colorCode="text-green-600"/>
            <RowCard name="Issues"
              className="cursor-pointer"
              onClick={() => setAndGo('bookings', { status: 'error' })}
              value={title?.booking_counts['errors'] ?? loadingIcon} colorCode="text-red-600"/>
          </DashboardRow>
        </div>
      }
      <ReleaseTableIndex title={title} header={false}/>
    </>
  )
}

export default Summary

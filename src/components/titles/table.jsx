import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import { Table, ButtonDropdown, Modal, Input } from '@bitcine/cinesend-theme'
import { convertToLocal, convertToShorthand } from '/src/helpers/convert_date'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '/src/hooks/auth'
import { useTitles } from '/src/hooks/titles'
import { useQuery } from '@tanstack/react-query'
import ResetTableFilters from '/src/components/reset_table_filters'


const TITLES_API = '/api/titles'

function TitlesTable ({ updateTitle = (title) => {} }) {
  const { checkPermission } = useAuth()
  const { deleteTitle } = useTitles()

  const headerDefaults = { search: '', page: 0, take: 15, sortBy: 'friendly_title', sortDirection: 'ASC' }

  const defaults = localStorage.getItem(TITLES_API) !== null
    ? JSON.parse(localStorage.getItem(TITLES_API))
    : headerDefaults

  const [queries, setQueries] = useState(defaults)
  const [checked, setChecked] = useState([])
  const [idToDelete, setIdToDelete] = useState(null)
  const [titleToDelete, setTitleToDelete] = useState('')
  const [deleteConfirm, setDeleteConfirm] = useState('')
  const [deleteModal, setDeleteModal] = useState(false)
  const [filter, setFilter] = useState({})

  const params = new URLSearchParams(queries).toString()
  const url = `${TITLES_API}?${params}`

  const setQuery = (data, isFilter = false, isReset = false) => {
    const newQueries = { ...queries, ...data }
    if (isFilter && data === null || isReset) {
      delete newQueries.studio
      delete newQueries.status
      setFilter({})
    }
    // if a page wasn't clicked, and anything else was changed reset page to 0.
    if (data && !data.page) {
      newQueries.page = 0
    }
    setQueries(newQueries)
    localStorage.setItem(TITLES_API, JSON.stringify(newQueries))
  }

  const { data: titles, error } = useQuery({ queryKey: [url] })
  const navigate = useNavigate()

  // fetch org list filtered by studio
  const orgUrl = '/api/organizations'
  const { data } = useQuery({ queryKey: [orgUrl] })

  const studioList = data?.data.map(org => ({ key: org.id, label: org.name }))

  // process studio to key label objs for dynamic filter
  const studioOptions = studioList ?? [
    { key: '2', label: 'Disney' },
    { key: '8', label: 'Paramount' },
    { key: '4', label: 'Sony' }
  ]

  const filterOptions = [
    {
      label: 'Status',
      key: 'status',
      type: 'radio_buttons',
      options: [
        { key: '1', label: 'Active' },
        { key: '2', label: 'Archived' }
      ]
    },
    {
      label: 'Studio',
      key: 'studio',
      type: 'checkboxes',
      options: studioOptions
    }
  ]
  useEffect(() => {
    if (checked.length > 0) {
      updateTitle(`Titles (${checked.length})`)
    }
    else {
      updateTitle('Titles')
    }
  }, [checked])

  return (
    <>
      <Table
        status={{
          pending: !titles,
          pendingMessage: 'Loading titles...',
          error: error
        }}
        header={{
          customElement: <ResetTableFilters filterCallback={() => {
            setQuery(headerDefaults, false, true);
          }}
          />,
          columns: [
            { text: 'Title', key: 'friendly_title' },
            { text: 'Studio' },
            { text: 'Total Bookings' },
            { text: 'Booking Summary' },
            { text: 'Release Date', key: 'release_date' },
            { text: 'Created', key: 'created_at' }
          ],
          filtering: {
            options: filterOptions,
            filters: filter,
            onFiltersChange: value => {
              setFilter(value)
              setQuery(value, true)
            }
          },
          searching: {
            search: queries.search,
            searchPlaceholder: 'Search...',
            onSearch: search => setQuery({ search }),
            rightIcon: {
              icon: 'close',
              onClick: () => setQuery({search: ''}),
              tooltip: {
                text: "Clear search"
              }
            }
          },
          sorting: {
            options: [
              { key: 'friendly_title', label: 'Title' },
              { key: 'release_date', label: 'Release Date' },
              { key: 'created_at', label: 'Created Date' }
            ],
            key: queries.sortBy,
            sortingKey: queries.sortBy,
            direction: queries.sortDirection,
            onSortChange: (sort) => setQuery({ sortBy: sort.key, sortDirection: sort.direction })
          },
          // checkbox: {
          //   checked: checked.length > 0,
          //   indeterminate: titles?.total && checked.length !== titles?.total,
          //   onChange: () => {
          //     if (checked.length === 0) {
          //       setChecked(titles?.data.map(({ id }) => id))
          //     }
          //     else {
          //       setChecked([])
          //     }
          //   }
          // }
        }}
        widths={[200, 200, 100, 100, 100, 100, 55]}
        body={{
          data: titles?.data,
          row: {
            spaced: true,
            onClick: checkPermission('view-titles') ? (event, data) => navigate(`/titles/${data.id}`) : null,
            // checkbox: {
            //   checked: (data, index) => checked.includes(data.id),
            //   onChange: (data, index) => (checked.includes(data.id)
            //     ? setChecked(checked.filter(i => i !== data.id))
            //     : setChecked([...checked, data.id]))
            // },
            render: [
              ({ friendly_title }) => <div className="font-medium">{friendly_title}</div>,
              ({ organization }) => organization?.name,
              ({ booking_counts }) =>
                (Object.values(booking_counts ?? []).reduce((partialSum, a) => partialSum + a, 0)
                  ? Object.values(booking_counts ?? []).reduce((partialSum, a) => partialSum + a, 0)
                  : 'None'),
              ({ booking_counts }) =>
                (Object.values(booking_counts ?? []).reduce((partialSum, a) => partialSum + a, 0)
                  ? <div className={'flex flex-row font-medium space-x-2'}>
                    <div className={'text-blue-600 pr-2 border-r-2 border-gray-200'} title={'Booked'}>
                      {booking_counts['pending']}</div>
                    <div className={'text-yellow-600 pr-2 border-r-2 border-gray-200'} title={'Pending'}>
                      {booking_counts['in_progress']}</div>
                    <div className={'text-green-600 pr-2 border-r-2 border-gray-200'} title={'Completed'}>
                      {booking_counts['completed']}</div>
                    <div className={'text-red-600 pr-2'} title={'Issues'}>{booking_counts['errors']}</div>
                  </div>
                  : 'None'),
              ({ release_date }) => (release_date ? convertToShorthand(dayjs(release_date).tz('GMT')) : 'N/A'),
              ({ created_at }) => convertToLocal(created_at),
              data => (checkPermission('delete-titles') ? <ButtonDropdown
                button={{
                  minimal: true, icon: 'more_vert', type: 'neutral'
                }}
                dropdown={{
                  content: [
                    {
                      text: 'Delete Title',
                      icon: 'delete_forever',
                      onClick: () => {
                        setIdToDelete(data.id)
                        setDeleteConfirm('')
                        setTitleToDelete(data.friendly_title)
                        setDeleteModal(true)
                      },
                      show: checkPermission('delete-titles')
                    }
                  ].filter(opt => opt.show)
                }}/> : <></>)

            ]
          },
          empty: {
            title: 'No titles found!',
            text: 'Refine your filters.',
            icon: 'movie'
          }
        }}
        paginate={{
          totalRows: titles?.total,
          currentPage: titles?.current_page - 1,
          rowsPerPage: titles?.per_page,
          onPageChange: page => setQuery({ page }),
          onRowsPerPageChange: take => setQuery({ take })
        }}
      />
      {deleteModal && <Modal
        className={'w-1/4'}
        onClose={() => {
          setDeleteModal(false)
          setIdToDelete(null)
        }}
        confirmButton={{
          text: 'Click to Confirm',
          disabled: deleteConfirm !== 'DELETE',
          onClick: () => {
            deleteTitle({ titleID: idToDelete })
            setDeleteModal(false)
          }
        }}
        header={'Confirm Deletion'}
      >
        <p className={'pb-4'}>Please confirm your request to delete
          <span className={'font-bold mx-2'}>{titleToDelete}</span>
          by typing DELETE in the box below.</p>
        <Input label={'type DELETE to confirm:'}
               value={deleteConfirm}
               onChange={e => setDeleteConfirm(e.target.value)}/>
      </Modal>}
    </>
  )
}

export default TitlesTable

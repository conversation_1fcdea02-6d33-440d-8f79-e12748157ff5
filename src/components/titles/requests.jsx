import { useState } from 'react'
import axios from '/src/lib/axios'
import { Button, Input, Table, ButtonDropdown } from '@bitcine/cinesend-theme'
import { convertToLocalDateTime } from '/src/helpers/convert_date'
import CreateRequestModal from './create_request_modal'
import dayjs from 'dayjs'
import { useAuth } from '/src/hooks/auth'
import { useQuery, useQueryClient } from '@tanstack/react-query'

const LINKS_URL = '/api/titles/{titleId}/requests'
const UPDATE_LINK_URL = '/api/titles/{titleId}/requests/{linkId}'

function Requests ({ title }) {

  const [modal, setModal] = useState(false)
  const queryClient = useQueryClient()
  const dataUrl = LINKS_URL.replace('{titleId}', title.id)
  const { data: links, error } = useQuery({ queryKey: [dataUrl]})
  const mutate = queryClient.invalidateQueries({ queryKey: [dataUrl]})
  const { checkPermission } = useAuth()

  return (
    <div className="flex flex-col space-y-4 max-w-full">
      <div className="flex justify-end space-x-4">
        {checkPermission('create-requests') && <Button onClick={() => {
          setModal(true)
        }}>Create Request</Button>}
      </div>

      <Table
        status={{
          pending: !links,
          pendingMessage: 'Loading requests...',
          error: error
        }}
        header={{
          columns: [
            { text: 'URL' },
            { text: '#' },
            { text: 'Expires At' },
            { text: 'Created At' },
            { text: '' }
          ]
        }}
        widths={['auto', 100, 260, 260, 65]}
        body={{
          data: links?.data ?? [],
          row: {
            spaced: true,
            render: [
              ({ url, is_expired }) => <Input value={url} disabled={true} copyIcon={!is_expired}/>,
              ({ upload_count }) => upload_count ?? 0,
              ({ expires_at, is_expired }) => <div className={is_expired ? 'text-red-600' : ''}>{convertToLocalDateTime(
                expires_at)}</div>,
              ({ created_at }) => convertToLocalDateTime(created_at),
              (data) => <ButtonDropdown
                button={{ minimal: true, icon: 'more_vert', type: 'neutral' }}
                dropdown={{
                  content: [
                    {
                      text: 'Expire Link',
                      onClick: () => {
                        // shove is_expired=true to api
                        axios.put(UPDATE_LINK_URL.replace('{titleId}', title.id).replace('{linkId}', data.id),
                          { 'is_expired': true, 'expires_at': dayjs() }
                        ).then((res) => {
                          mutate()
                        })
                      }
                    },
                    {
                      text: 'Extend by 3 Days',
                      onClick: () => {
                        // shove is_expired=true to api
                        axios.put(UPDATE_LINK_URL.replace('{titleId}', title.id).replace('{linkId}', data.id),
                          { 'is_expired': false, 'expires_at': dayjs().add(3, 'day') }
                        ).then((res) => {
                          mutate()
                        })
                      }
                    }
                  ]
                }}/>
            ]
          },
          empty: {
            title: 'No request links made.',
            text: 'Create a request to invite a third party to upload content to this title.',
            icon: 'link'
          }
        }}
      />
      {modal && <CreateRequestModal title={title} onClose={() => {
        setModal(false)
        mutate()
      }}/>}
    </div>
  )
}

export default Requests

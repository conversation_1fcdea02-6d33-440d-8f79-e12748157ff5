import React, { useState } from 'react'
import { Button } from '@bitcine/cinesend-theme'
import DashboardRow from '/src/components/dashboards/dashboard_row'
import TableCard from '/src/components/dashboards/table_card'
import { useTitles } from '/src/hooks/titles'
import { useAuth } from '/src/hooks/auth'
import { useQuery } from '@tanstack/react-query'

const ORPHANS_URL = '/api/titles/orphans'

function Orphans ({ title }) {

  const { linkOrphan } = useTitles()
  const { checkPermission } = useAuth()

  const { data, error } = useQuery({ queryKey: [ORPHANS_URL] })

  const addButton = (data) => (
    <Button tertiary={true}
      disabled={(data.status !== 'completed') || !checkPermission('create-titles')} onClick={() => {
        linkOrphan({ asperaTransferID: data.id, titleID: title.id, invalidationKey: ORPHANS_URL })
      }}>Assign Content</Button>
  )

  return (
    <div className="flex flex-col space-y-4 max-w-full">
      <DashboardRow rowTitle={'Direct S3 Uploads:'}>
        <TableCard headers={[
          { isVisible: true, key: 'origin_name', label: 'Folder Name', width: 'w-1/2' },
          { isVisible: true, key: 'status', label: 'Status', width: 'w-1/4' },
          { isVisible: true, key: 'id', label: '', processor: (id, row = []) => addButton(row), width: 'w-1/4' }
        ]}
        values={data ?? []}/>
      </DashboardRow>

    </div>
  )
}

export default Orphans

import dayjs from 'dayjs'
import React, { useState } from 'react'
import { But<PERSON>, DatePicker, Input } from '@bitcine/cinesend-theme'
import { useTitles } from '/src/hooks/titles'
import ErrorMessages from '/src/components/error_messages'
import SelectOrganization from '/src/components/organizations/select_organization'
import getUpdatedData from '/src/helpers/get_updated_data'
import { useAuth } from '/src/hooks/auth'

const Details = ({ title }) => {

  const { checkPermission } = useAuth()
  const canUpdate = checkPermission('update-titles')

  const [errors, setErrors] = useState([])
  const [data, setData] = useState(title)
  const [pending, setPending] = useState(false)
  const update = newData => {
    setData({ ...data, ...newData })
  }

  const { updateTitle, deleteTitle } = useTitles()
  const save = () => {
    setPending(true)
    updateTitle({
      titleID: title.id,
      ...getUpdatedData(title, data),
      onComplete: () => setPending(false),
      setErrors: errors => {
        console.log(errors)
        setPending(false)
        setErrors(errors)
      }
    })
  }
  return (
    <div className="flex flex-col space-y-4 max-w-2xl">
      <Input
        disabled={!canUpdate}
        label="Name of Title"
        placeholder="The Wizard of Oz"
        value={data.friendly_title}
        onChange={e => update({ friendly_title: e.target.value })}
      />
      <SelectOrganization
        disabled={!canUpdate}
        label={'Studio (Owner)'}
        value={data.organization_id}
        onChange={organization_id => update({ organization_id })}/>
      <DatePicker
        disabled={!canUpdate}
        label='Initial Release Date'
        date={dayjs(data.release_date).tz('GMT').toString()}
        showJumpToYear={true}
        dateFormat='MMMM d, yyyy'
        showTimeSelect={false}
        onChange={date => update({ release_date: dayjs(date).hour(0).minute(0).toString() })}/>
      <Input
        disabled={!canUpdate}
        label="DC Hub ID"
        placeholder="0123456"
        value={data.dchub_title_id}
        onChange={e => update({ dchub_title_id: e.target.value })}
      />

      <div className="flex justify-between">
        <Button
          onClick={save}
          disabled={JSON.stringify(data) === JSON.stringify(title) || pending}>
          Save
        </Button>
      </div>
      <ErrorMessages errors={errors}/>
    </div>
  )

}

export default Details

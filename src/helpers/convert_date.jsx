import dayjs from 'dayjs'

export const convertToLocal = (date, options = { withTimezone: false, withTime: false }) =>
  (date
    ? dayjs.utc(date).local().format(`ddd, MMM D, YYYY ${options.withTime ? '[at] h:mma z' : ''}`)
      + (options.withTimezone ? ` (${dayjs.tz.guess()})` : '')
    : 'N/A')

export const convertToLocalDateTime = date => convertToLocal(date, { withTime: true })

export const convertToLocalWithTimezone = date => convertToLocal(date, { withTimezone: true })

export const convertToUTC = date => dayjs.utc(date).toString()

export const convertToFromNow = date => dayjs.utc(date).local().fromNow()

export const convertToShorthand = date =>
  (date ? dayjs.utc(date).local().format('L') : 'N/A')

export const convertToShorthandWithTime = date =>
  (date ? dayjs.utc(date).local().format('L h:mm A') : 'N/A')

export const convertSecondsToHoursMinutes = seconds => {
  let str = ''
  if (seconds < 60) {
    return `${seconds} seconds`
  }
  let minutes = Math.floor(seconds / 60)
  let hours = Math.floor(minutes / 60)
  if (hours > 0) {
    minutes = minutes - hours * 60
  }
  return `${hours}h ${minutes}min`

}

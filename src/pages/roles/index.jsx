import AppLayout from '/src/components/layouts/app_layout'
import RolesList from '/src/components/settings/roles_list'
import PermissionsList from '/src/components/settings/permissions_list'
import { useState } from 'react'
import { useRoles } from '/src/hooks/roles'
import { useQuery, useQueryClient } from '@tanstack/react-query'

function Roles () {
  const { createRole, updateRole, createPermission } = useRoles()

  const [selectedRoleId, setSelectedRoleId] = useState(null)
  const [selectedPermissions, setSelectedPermissions] = useState([])

  const queryClient = useQueryClient()

  const { data: roles, isLoading: rolesIsLoading } = useQuery({ queryKey: ['/api/roles']})
  const rolesMutate = () => queryClient.invalidateQueries({ queryKey: ['/api/roles']})

  const { data: permissions, isLoading: permissionsIsLoading } = useQuery({ queryKey: ['/api/permissions']})
  const permissionsMutate = () => queryClient.invalidateQueries({ queryKey: ['/api/permissions']})

  return (
    <AppLayout
      contentClassNames={'p-8'}
      background={'bg-white'}
      header={'Roles and Permissions'}>
      <div className={'flex flex-row w-full space-x-2 bg-white'}>
        {roles && <RolesList list={roles} roleId={selectedRoleId}
          onCheck={(roleId) => {
            setSelectedRoleId(roleId)
            setSelectedPermissions(roles.find(r => r.id === roleId)?.permissions)
          }}
          onCreate={(name) => {
            createRole({name, onComplete: () => rolesMutate() })
          }}
        />}
        {permissions && <PermissionsList roleId={selectedRoleId} list={permissions} selected={selectedPermissions}
          onSave={(newList) => {
            let permissionIds = newList.map(({ id }) => id)

            // post to selected RoleID an array of Selected Permissions// then mutate roles when done saving.
            updateRole({roleId:selectedRoleId, permissionIds: permissionIds,
              onComplete: () => rolesMutate() })
          }}
          onCreate={(name) => {
            // post // then mutate permissions
            createPermission({name, onComplete: () => { permissionsMutate() }})
          }}
        />}
      </div>
    </AppLayout>
  )
}

export default Roles

import AppLayout from '/src/components/layouts/app_layout'
import Details from '/src/components/settings/details'
import Security from '/src/components/settings/security'
import { useAuth } from '/src/hooks/auth'
import { Navigate, Route, Routes } from 'react-router-dom'
import ApiTokens from '/src/components/users/api_tokens'

function User () {
  const { user } = useAuth()
  return (
    <AppLayout
      contentClassNames={'p-8'}
      background={'bg-white'}
      header={'My Account'}
      tabs={
        [
          { name: 'My Profile', url: 'profile'},
          { name: 'Security', url: 'security' },
          { name: 'API Tokens', url: 'api-tokens' }
        ].filter(opt => !opt.hide).map(({ name, url }) => ({
          name,
          to: `/settings/${url}`
        }))
      }>
      <Routes>
        <Route exact path="" element={<Navigate to="profile" replace/>} />
        <Route path="profile" element={<Details user={user}/>} />
        <Route path="security" element={<Security user={user}/>} />
        <Route path="api-tokens" element={<ApiTokens user={user}/>} />
      </Routes>
    </AppLayout>
  )
}

export default User

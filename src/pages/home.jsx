import { NavLink } from 'react-router-dom'
import Logo from '/src/images/dcdc-logo-white.png'

function Home () {
  return (
    <div className="relative flex items-top justify-center
      min-h-screen bg-gray-900 sm:items-center sm:pt-0">
      <div className="max-w-6xl mx-auto sm:px-6 lg:px-8">
        <div className="flex flex-col justify-center pt-8 sm:justify-start sm:pt-0">
          <img
            alt="react-logo"
            src={Logo}
            className="h-40 w-auto text-gray-700 sm:h-20 ml-10"
          />
        </div>
        <div className="flex justify-center">
          <NavLink
            to="/login"
            className="mt-4 text-2xl text-primary-100 underline">Login</NavLink>
        </div>
      </div>
    </div>
  )
}

export default Home

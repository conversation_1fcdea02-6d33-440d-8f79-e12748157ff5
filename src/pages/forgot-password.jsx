import FullLogo from '/src/components/logos/full_logo'
import AuthCard from '/src/components/auth_card'
import AuthSessionStatus from '/src/components/auth_session_status'
import AuthValidationErrors from '/src/components/auth_validation_errors'
import GuestLayout from '/src/components/layouts/guest_layout'
import { Input, Button } from '@bitcine/cinesend-theme'
import { useAuth } from '/src/hooks/auth'
import { useState } from 'react'
import { NavLink } from 'react-router-dom'

const ForgotPassword = () => {
  const { forgotPassword } = useAuth({ middleware: 'guest' })

  const [email, setEmail] = useState('')
  const [errors, setErrors] = useState([])
  const [status, setStatus] = useState(null)

  const submitForm = event => {
    event.preventDefault()
    forgotPassword({ email, setErrors, setStatus })
  }

  return (
    <GuestLayout>
      <AuthCard
        logo={
          <NavLink to="/">
            <FullLogo className="h-20 w-auto" />
          </NavLink>
        }>
        <div className="mb-4 text-sm text-gray-600">
          Forgot your password? No problem. Just let us know your
          email address and we will email you a password reset link
          that will allow you to choose a new one.
        </div>
        {/* Session Status */}
        <AuthSessionStatus className="mb-4" status={status} />
        {/* Validation Errors */}
        <AuthValidationErrors className="mb-4" errors={errors} />
        <form onSubmit={submitForm}>
          <Input
            disabled={!!status}
            label='Email'
            type="email"
            value={email}
            onChange={event => setEmail(event.target.value)}
          />
          <div className="flex items-center justify-between mt-4">
            <NavLink
              to="/login"
              className="underline text-sm text-gray-600 hover:text-gray-900"
            >
              Back to login
            </NavLink>
            <Button type='warning' disabled={!email || !!status }>Email Password Reset Link</Button>
          </div>
        </form>
      </AuthCard>
    </GuestLayout>
  )
}

export default ForgotPassword

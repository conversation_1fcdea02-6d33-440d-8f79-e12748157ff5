import { Status, But<PERSON>, Table } from '@bitcine/cinesend-theme'
import { useState } from 'react'
import { Link } from 'react-router-dom'
import GuestLayout from '/src/components/layouts/guest_layout'
import FullLogo from '/src/components/logos/full_logo'
import UploaderCard from '/src/components/uploader_card'
import { withAspera } from '/src/hooks/with_aspera'
import humanFileSize from '/src/helpers/human_file_size'
import { useQuery } from '@tanstack/react-query'

function PublicUpload () {
  const { asperaWeb, createPublicUpload } = withAspera()

  const params = new URLSearchParams(window.location.search);

  const verifyUrl = () => {
    let str = params.get('signedUrl');
    if (str === '' || str.trim() === '') { return false }
    try {
      return atob(str)
    }
    catch (err) {
      return null
    }
  }

  const [pending, setPending] = useState(false)
  const [errors, setErrors] = useState([])
  const create = () => {
    setPending(true)
    asperaWeb.showSelectFolderDialog({
      success: ({ dataTransfer }) => {
        if (!dataTransfer.files || !dataTransfer.files.length) {
          setPending(false)
          return false
        }
        createPublicUpload({
          url: data.upload_url, dataTransfer, setErrors,
          onComplete: () => setPending(false)
        })
      }, error: res => {
        //addMessage(res.error.user_message || 'Failed to upload, please try again', 'error')
        setPending(false)
      }
    })
  }

  const { data, error } = useQuery({ queryKey: [verifyUrl()] })

  return (<GuestLayout>
    <UploaderCard
      logo={<Link to="/">
        <FullLogo className="h-20 w-auto"/>
      </Link>}>
      <Status pending={!data && !error}>
        {verifyUrl() === null &&
          <div className="text-red-700">
            The link used is malformed. Please try again and make sure there are no line breaks.
          </div>}
        {error && (error.response.status === 419) &&
          <div className="text-red-700">This link has expired. Please contact the sender for a new link.</div>}
        {error && (error.response.status === 401) &&
          <div className="text-red-700">This link is invalid. Please contact the sender for a new link.</div>}
        
        {data && !error && 
          <div className='text-sm space-y-8'>
            <h4 className="text-primary-600 flex justify-center">
              Upload for "{data.title}"
            </h4>
            <p className="justify-center w-full flex">
              Please use Aspera to upload the distribution DCPs for {data.title}
            </p>

            <div className='flex justify-center'>
              <Button
                icon="upload"
                disabled={pending || error || !data?.upload_url}
                onClick={() => create()}>Upload DCP</Button>
            </div>

            <Table
              status={{
                pending: !data,
                pendingMessage: 'Loading uploads...'
              }}
              header={{
                columns: [
                  { text: 'Folder Name' },
                  { text: 'Status' }
                ]
              }}
              widths={['auto', 100]}
              body={{
                data: data?.uploads ?? [],
                row: {
                  spaced: true,
                  render: [
                    ({ origin_name, aspera_statistics: stat }) =>
                      <div className='flex flex-col'>{origin_name}
                        <div className='flex items-center text-2xs text-gray-600 space-x-1'>
                          <div>{humanFileSize(stat.bytes_written)} / {humanFileSize(stat.bytes_expected)}</div>
                          <div>@ {(stat.calculated_rate_kbps / 1024).toFixed()} mbps</div>
                        </div>
                      </div>,
                    ({ status }) => <div className='capitalize'>{status}</div>
                  ]
                },
                empty: {
                  title: 'No uploads yet.',
                  icon: 'upload'
                }
              }}
            />
            <p className='flex justify-center text-xs'>
              If you have any issues, please contact the sender of this link.
            </p>
          </div>
        }
      </Status>
    </UploaderCard>
  </GuestLayout>)
}

export default PublicUpload

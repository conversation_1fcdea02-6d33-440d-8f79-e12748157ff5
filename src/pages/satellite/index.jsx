import {useEffect, useState} from "react";
import {Route, Routes} from "react-router-dom";
import {Button, Modal, Select} from '@bitcine/cinesend-theme'
import {useSnackbar} from "/src/hooks/snackbar";
import {useAuth} from '/src/hooks/auth'
import AppLayout from "/src/components/layouts/app_layout";
import Transfers from "/src/components/fazzt/transfers";
import History from "/src/components/fazzt/history";
import axios from "/src/lib/axios";
import useTransfers from '/src/components/fazzt/use_transfers'

function SatelliteQueue() {
    const {checkPermission} = useAuth()
    const [availableReleases, setAvailableReleases] = useState([])
    const [selectedRelease, setSelectedRelease] = useState(false)
    const [modalVisible, setModalVisible] = useState(false)
    const [buttonDisabled, setButtonDisabled] = useState(false)
    const { addMessage } = useSnackbar()

    const { invalidateTransfers } = useTransfers()

    // fetch a list of addable releases.
    useEffect(() => {
        axios.get(`api/fazzt/available-to-queue`).then(res => setAvailableReleases(res.data))
    }, []);

    return (
        <AppLayout
            contentClassNames={'p-8'}
            header={'Satellite Queue'}
            buttons={[<Button
                disabled={availableReleases.length === 1}
                onClick={() => setModalVisible(true)}
                icon={'add'}
                iconPosition={'left'}
            >Add Release to Queue</Button>]}
            tabs={
                [
                    {link: 'active'},
                    {link: 'completed'}
                ].map(opt => ({
                    name: opt.link,
                    to: `/satellite/${opt.link}`,
                    visible: checkPermission(`view-satellite`)
                }))
            }>
            <Routes>
                <Route exact path={"active"} element={<Transfers/>}/>
                <Route exact path={"completed"} element={<History/>}/>
            </Routes>

            {modalVisible && (
                <Modal header={"Add Release to Queue"}
                       onClose={() => {
                           setModalVisible(false)
                           setSelectedRelease(false)
                           setButtonDisabled(false)
                        }}>
                    <div className={'flex flex-col space-y-4'}>
                        <p>Select Available Release</p>
                        <Select
                            disabled={buttonDisabled}
                            options={availableReleases}
                            value={selectedRelease}
                            onChange={(value) => setSelectedRelease(value)}
                        ></Select>
                        <Button
                            disabled={!selectedRelease || buttonDisabled}
                            className={'w-1/3'}
                            onClick={() => {
                                setButtonDisabled(true)
                                // submit selected release to API
                                axios.post('api/fazzt/add-to-queue', {fcId: selectedRelease.value}).then(res => {
                                    addMessage("Added to Queue")
                                }).catch(err => {
                                    console.log(err)
                                    addMessage("Whoops, failed to add to queue.", 'error')
                                }).finally(() => {
                                    setButtonDisabled(false)
                                    setSelectedRelease(false)
                                    setModalVisible(false)
                                    invalidateTransfers()
                                })
                            }}
                        >Add Release To Queue</Button>
                    </div>
                </Modal>)}
        </AppLayout>
    )
}

export default SatelliteQueue
import { Routes, Route, Navigate, useLocation } from 'react-router-dom'
import AppLayout from '/src/components/layouts/app_layout'
import { useParams } from 'react-router-dom'
import Details from '/src/components/sites/general'
import Bookings from '/src/components/sites/bookings'
import Analytics from '/src/components/sites/analytics'
import Hardware from '/src/components/hardware'
import Device from '/src/components/device'
import Jobs from '/src/components/sites/jobs'
import StatusLog from '/src/components/sites/status_log'
import SiteStatus from '/src/components/sites/site_status'
import Contacts from '/src/components/sites/contacts'
import { useAuth } from '/src/hooks/auth'
import { useQuery, useQueryClient } from '@tanstack/react-query'

function Site () {

  const { checkPermission } = useAuth()
  const { siteID } = useParams()
  const queryClient = useQueryClient()

  const apiUrl = `/api/cinemas/${siteID}`

  const { data } = useQuery({ queryKey: [apiUrl] })

  const site = data?.data

  const hasDevice = site && site.media_managers.length > 0
  const pathname = useLocation().pathname
  const isLocalContent = pathname.includes('local-content')

  return (
    <AppLayout
      contentClassNames={(!isLocalContent ? 'p-8' : '')}
      breadcrumbs={[{ to: '/sites', text: 'Sites' }, { text: site?.name }]}
      buttons={[<SiteStatus disabled={!checkPermission('update-cinemas')} key={1} site={site}/>]}
      pending={!site}
      header={site?.name ?? 'Loading'}
      tabs={
        [
          { name: 'general' },
          { name: 'bookings' },
          { name: 'equipment', hide: !hasDevice },
          { name: 'local-content', hide: !hasDevice },
          { name: 'jobs', hide: !hasDevice },
          { name: 'analytics', hide: !hasDevice },
          { name: 'contacts'},
          { name: 'status-log'}
        ].filter(opt => !opt.hide).map(({ name }) => ({
          name,
          to: `/sites/${siteID}/${name}`
        }))
      }>
      <Routes>
        <Route exact path="" element={<Navigate to="general" replace/>}/>
        <Route path="general" element={<Details site={site}/>}/>
        <Route path="equipment" element={<Hardware site={site} deviceId={site?.primary_media_manager?.id}/>}/>
        <Route path="bookings" element={<Bookings site={site}/>}/>
        <Route path="local-content" element={<Device site={site}/>}/>
        <Route path="jobs" element={<Jobs site={site}/>}/>
        <Route path="analytics" element={<Analytics site={site}/>}/>
        <Route path="contacts" element={<Contacts site={site}/>}/>
        <Route path="status-log" element={<StatusLog site={site}/>}/>
      </Routes>
    </AppLayout>
  )
}

export default Site

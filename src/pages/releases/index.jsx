import AppLayout from '/src/components/layouts/app_layout'
import { Routes, Route } from 'react-router-dom'
import { useState } from 'react'
import ReleasesTable from "/src/components/releases/releases_table";
import { useAuth } from '/src/hooks/auth'
import Release from "../release";
import { v4 as uuid } from 'uuid'

function Releases () {
    const { checkPermission } = useAuth()
    const [dynamicTitle, setDynamicTitle] =  useState('Releases')
    return (
        <AppLayout
            contentClassNames={'p-8'}
            header={dynamicTitle}
            tabs={
                [
                    { link: 'all' },
                    { link: 'pinned'}
                ].map(opt => ({
                    name: opt.link,
                    to: `/releases/${opt.link}`,
                    visible: checkPermission(`view-releases`)
                }))
            }>
            <Routes>
                <Route exact path={"all"} element={<ReleasesTable updateTitle={setDynamicTitle} pinned={false}/>}/>
                <Route exact path={"pinned"} element={<ReleasesTable updateTitle={setDynamicTitle} pinned={true}/>}/>
            </Routes>
        </AppLayout>

    )
}

export default Releases

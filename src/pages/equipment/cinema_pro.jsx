import AppLayout from '/src/components/layouts/app_layout'
import { Navigate, Route, Routes, useParams } from 'react-router-dom'
import CinemaProDetails from '/src/components/equipment/cinema_pro_details'
import { useQuery } from '@tanstack/react-query'

function CinemaProPage () {

  const { deviceID } = useParams()
  const apiUrl = `/api/cinema-pro-servers/${deviceID}`

  const { data } = useQuery({ queryKey: [apiUrl], refetchOnWindowFocus: true })

  return (
    <AppLayout
      contentClassNames={'p-8'}
      pending={!data?.cinema_pro}
      header={data?.cinema_pro.serial_number}
      breadcrumbs={[
        { to: '/equipment/cinema-pros', text: 'Cinema Pro Servers' },
        { text: data?.cinema_pro.serial_number }
      ]}
      tabs={
        [
          { name: 'Asset', url: 'asset'},
        ].filter(opt => !opt.hide).map(({ name, url }) => ({
          name,
          to: `/cinema-pro-servers/${deviceID}/${url}`
        }))
      }>
      <Routes>
        <Route exact path="" element={<Navigate to="asset" replace/>} />
        <Route path="asset" element={<CinemaProDetails/>} />
      </Routes>
    </AppLayout>

  )
}

export default CinemaProPage

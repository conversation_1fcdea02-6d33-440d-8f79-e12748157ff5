import AppLayout from '/src/components/layouts/app_layout'
import { Navigate, Route, Routes, useParams } from 'react-router-dom'
import ServerSettings from '/src/components/equipment/server_settings'
import ServerNetworking from '/src/components/equipment/server_networking'
import ServerStorage from '/src/components/equipment/server_storage'
import LivestreamDetails from '/src/components/equipment/livestream_details'
import { useQuery } from '@tanstack/react-query'

function LiveStreamDecoderPage () {

  const { deviceID } = useParams()
  const apiUrl = `/api/apple-televisions/${deviceID}`

  const { data } = useQuery({ queryKey: [apiUrl], refetchOnWindowFocus: false })

  return (
    <AppLayout
      contentClassNames={'p-8'}
      pending={!data?.appleTelevision}
      header={data?.appleTelevision.serial_number}
      breadcrumbs={[
        { to: '/equipment/livestream-decoders', text: 'Livestream Decoders' },
        { text: data?.appleTelevision.serial_number }
      ]}
      tabs={
        [
          { name: 'Asset', url: 'asset'},
          { name: 'System Settings', url: 'settings' },
          { name: 'Networking', url: 'networking' },
          { name: 'Storage', url: 'storage' }
        ].filter(opt => !opt.hide).map(({ name, url }) => ({
          name,
          to: `/livestream-decoder/${deviceID}/${url}`
        }))
      }>
      <Routes>
        <Route exact path="" element={<Navigate to="asset" replace/>} />
        <Route path="asset" element={<LivestreamDetails/>} />
        <Route path="settings" element={<ServerSettings/>} />
        <Route path="networking" element={<ServerNetworking/>} />
        <Route path="storage" element={<ServerStorage />} />
      </Routes>
    </AppLayout>

  )
}

export default LiveStreamDecoderPage

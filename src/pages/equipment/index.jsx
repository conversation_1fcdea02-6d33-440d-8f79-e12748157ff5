import AppLayout from '/src/components/layouts/app_layout'
import { useState } from 'react'
import { Navigate, Route, Routes } from 'react-router-dom'
import AppleTvsTable from '/src/components/equipment/livestream_table'
import CinemaProTable from '/src/components/equipment/cinema_pro_table'
import ServersTable from '/src/components/equipment/servers_table'
import { useAuth } from '../../hooks/auth'

function Equipment () {
  const { checkPermission } = useAuth()
  const [dynamicTitle, setDynamicTitle] = useState('Equipment')
  return (
    <AppLayout
      contentClassNames={'p-8'}
      header={dynamicTitle}
      tabs={
        [
          { name: 'Catch Servers', url: 'servers' },
          { name: 'Livestream Decoders', url: 'livestream-decoders' },
          { name: 'Cinema Pro Servers', url: 'cinema-pros', hide: !checkPermission('view-cinemapros') }
        ].filter(opt => !opt.hide).map(({ name, url }) => ({
          name,
          to: `/equipment/${url}`
        }))
      }>
      <Routes>
        <Route exact path="" element={<Navigate to="servers" replace/>}/>
        <Route path="servers"
          element={<ServersTable apiUrl={'/api/media-managers'} updateTitle={setDynamicTitle}/>}/>
        <Route path="livestream-decoders"
          element={<AppleTvsTable apiUrl={'/api/apple-televisions'} updateTitle={setDynamicTitle}/>}/>
        <Route path="cinema-pros"
          element={<CinemaProTable apiUrl={'/api/cinema-pro-servers'} updateTitle={setDynamicTitle}/>}/>
      </Routes>
    </AppLayout>

  )
}

export default Equipment

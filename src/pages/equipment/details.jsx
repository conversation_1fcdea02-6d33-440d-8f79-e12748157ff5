import AppLayout from '/src/components/layouts/app_layout'
import { useEffect, useState } from 'react'
import { Navigate, Route, Routes, useParams } from 'react-router-dom'
import ServerDetails from '/src/components/equipment/server_details'
import ServerSettings from '/src/components/equipment/server_settings'
import ServerNetworking from '/src/components/equipment/server_networking'
import ServerStorage from '/src/components/equipment/server_storage'
import EquipmentStatus from '/src/components/equipment/equipment_status'
import { useQuery } from '@tanstack/react-query'

function ServerPage () {

  const { deviceID } = useParams()
  const apiUrl = `/api/media-managers/${deviceID}`

  const { data } = useQuery({ queryKey: [apiUrl], refetchOnWindowFocus: true })

  return (
    <AppLayout
      contentClassNames={'p-8'}
      buttons={[<EquipmentStatus key={1}
        status={data?.mediaManager.is_online}
        updated={data?.mediaManager.status_updated_at}/>]}
      pending={!data?.mediaManager}
      header={data?.mediaManager.serial_number}
      breadcrumbs={[
        { to: '/equipment/servers', text: 'Catch Servers' },
        { text: data?.mediaManager.serial_number }
      ]}
      tabs={
        [
          { name: 'Asset', url: 'asset'},
          { name: 'System Settings', url: 'settings' },
          { name: 'Networking', url: 'networking' },
          { name: 'Storage', url: 'storage' }
        ].filter(opt => !opt.hide).map(({ name, url }) => ({
          name,
          to: `/server/${deviceID}/${url}`
        }))
      }>
      <Routes>
        <Route exact path="" element={<Navigate to="asset" replace/>} />
        <Route path="asset" element={<ServerDetails/>} />
        <Route path="settings" element={<ServerSettings/>} />
        <Route path="networking" element={<ServerNetworking/>} />
        <Route path="storage" element={<ServerStorage />} />
      </Routes>
    </AppLayout>

  )
}

export default ServerPage

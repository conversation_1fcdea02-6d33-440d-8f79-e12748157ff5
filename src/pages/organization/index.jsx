import { Routes, Route, Navigate } from 'react-router-dom'
import AppLayout from '/src/components/layouts/app_layout'
import { useParams } from 'react-router-dom'
import Details from '/src/components/organizations/details'
import Users from '/src/components/organizations/users'
import RawBookingsTable from '/src/components/organizations/raw_bookings'
import Webhooks from '/src/components/organizations/webhooks'
import { useQuery } from '@tanstack/react-query'

function Organization () {

  const { organizationID } = useParams()
  // const { user, checkPermission } = useAuth()

  const apiUrl = `/api/organizations/${organizationID}`
  const { data } = useQuery({ queryKey: [apiUrl] })
  let organization = data?.data

  return (
    <AppLayout
      contentClassNames={'p-8'}
      header={organization?.name}
      pending={!organization}
      tabs={
        ['details', 'users', 'transactions', 'webhooks'].map(opt => ({
          name: opt,
          to: `/organizations/${organizationID}/${opt}`
        }))
      }>
      <Routes>
        <Route exact path="" element={<Navigate to="details" replace/>} />
        <Route path="details" element={<Details organization={organization}/>} />
        <Route path="users" element={<Users organization={organization}/>} />
        <Route path="transactions" element={<RawBookingsTable organization={organization}/>} />
        <Route path="webhooks" element={<Webhooks organization={organization}/>} />
      </Routes>
    </AppLayout>
  )
}

export default Organization

import AppLayout from '/src/components/layouts/app_layout'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useParams } from 'react-router-dom'
import Transactions from '/src/components/bookings/transactions'
import DeliveriesTable from '/src/components/transfers_table'
import Details from '/src/components/bookings/details'
import { useAuth } from '/src/hooks/auth'
import { useQuery } from '@tanstack/react-query'

function Booking () {

  const { checkPermission } = useAuth()
  const { bookingID } = useParams()

  const apiUrl = `/api/bookings/${bookingID}`
  const { data } = useQuery({ queryKey: [apiUrl]})

  const booking = data?.data
  const bookingName = booking ?
    `${booking?.title?.friendly_title} - ${booking?.cinema?.name}`
    : null

  return (
    <AppLayout
      contentClassNames={'p-8'}
      breadcrumbs={[{ to: '/bookings', text: 'Bookings' }, { text: bookingName }]}
      pending={!booking}
      tabs={
        [
          { name: 'details', hide: false},
          { name: 'transfers', hide: !checkPermission('view-deliveries') },
          { name: 'transactions', hide: booking?.transactions?.length === 0 }
        ].filter(opt => !opt.hide).map(({ name }) => ({
          name,
          to: `/bookings/${bookingID}/${name}`
        }))
      }>
      <Routes>
        <Route exact path="" element={<Navigate to="details" replace/>} />
        <Route path="details" element={<Details booking={booking}/>} />
        <Route path="transfers" element={<DeliveriesTable apiUrl={`/api/bookings/${booking?.id}/transfers`}/>}/>
        <Route path="transactions" element={<Transactions transactions={booking?.transactions}/>} />
      </Routes>
    </AppLayout>
  )
}

export default Booking

import AppLayout from '/src/components/layouts/app_layout'
import { useParams, Routes, Route, Navigate } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import {convertToLocal} from '/src/helpers/convert_date'
import Issues from '/src/components/releases/issues'
import Content from '/src/components/releases/content'
import IngestLetter from '/src/components/releases/ingest_letter'

function Release() {

  const { releaseID } = useParams()

  const { data } = useQuery({ queryKey: [`api/releases/${releaseID}`]})
  const release = data?.data

  return (
    <AppLayout
      contentClassNames={'p-8'}
      pending={!release}
      header={release?.title?.friendly_title}
      subheader={<div>
        <h2>{release?.package_name}</h2>
        <p>{release?.organization?.name}</p>
        <p>{`Release Date: ${convertToLocal(release?.title.release_date)}`}</p>
      </div>}
      tabs={
        ['issues', 'content', 'ingest letter'].map(opt => ({
          name: opt,
          to: `/release/${releaseID}/${opt.replace(/\s+/g, '-')}`
        }))
      }>
      <Routes>
        <Route exact path="" element={<Navigate to="issues" replace />} />
        <Route path="issues" element={<Issues issues={release?.issues} />} />
        <Route path="content" element={<Content titleID={release?.title.id} content={release?.content} />} />
        <Route path="ingest-letter" element={<IngestLetter url={release?.ingest_letter_url} />} />
      </Routes>
    </AppLayout>
  )

}

export default Release;
import Requests from '/src/components/titles/requests'
import { Routes, Route, Navigate } from 'react-router-dom'
import AppLayout from '/src/components/layouts/app_layout'
import { useParams } from 'react-router-dom'
import Details from '/src/components/titles/details'
import VersionsTableIndex from '/src/components/versions/table_index'
import Version from '/src/components/versions'
import Bookings from '/src/components/titles/bookings'
import { useAuth } from '/src/hooks/auth'
import Summary from '/src/components/titles/summary'
import ReleaseTableIndex from '/src/components/releases/release_index'
import Release from '/src/components/releases/release_by_title'
import { Select } from '@bitcine/cinesend-theme'
import { useTitles } from '../../hooks/titles'
import { useQuery, useQueryClient } from '@tanstack/react-query'

function Title () {
  const { checkPermission } = useAuth()
  const { titleID } = useParams()

  const { updateTitle } = useTitles()
  const queryClient = useQueryClient()

  const apiUrl = `/api/titles/${titleID}`
  const { data } = useQuery({ queryKey: [apiUrl]})
  // all single responses have a 'data' attribute with the model
  let title = data?.data

  const mutate = () => queryClient.invalidateQueries({ queryKey: [apiUrl] })

  const statusValues = [{ value: 1, label: 'Active' }, { value: 2, label: 'Archived' }]

  const status = <Select
    disabled={!checkPermission('edit-titles')}
    options={statusValues}
    value={statusValues.find(sv => sv.value === title?.status)}
    onChange={(value) => {
      updateTitle({ titleID: title?.id, status: value.value, onComplete: () => mutate(), setErrors: () => {} })
    }}
  />

  return (
    <AppLayout
      contentClassNames={'p-8'}
      header={title?.friendly_title}
      buttons={[status]}
      breadcrumbs={[{ to: '/titles', text: 'Titles' }, { text: title?.friendly_title }]}
      pending={!title}
      tabs={
        [
          { link: 'summary', permission: 'bookings' },
          { link: 'bookings', permission: 'bookings' },
          { link: 'metadata' },
          { link: 'releases', permission: 'versions' },
          { link: 'content', permission: 'versions' },
          { link: 'content-requests', permission: 'requests' }
          // 'reports'
        ].map(opt => ({
          name: opt.link,
          to: `/titles/${titleID}/${opt.link}`,
          visible: opt.link === 'metadata' || (opt.link !== 'metadata' && checkPermission(`view-${opt.permission}`))
        }))
      }>
      <Routes>
        <Route exact path="" element={<Navigate to="summary" replace/>}/>
        <Route path="summary" element={<Summary title={title}/>}/>
        <Route path="metadata" element={<Details title={title}/>}/>
        <Route path="releases" element={<ReleaseTableIndex title={title}/>}/>
        <Route path="releases/:releaseID" element={<Release/>}/>
        <Route path="content" element={<VersionsTableIndex title={title}/>}/>
        <Route path="content/:versionID" element={<Version/>}/>
        <Route path="bookings" element={<Bookings title={title}/>}/>
        <Route path="content-requests" element={<Requests title={title}/>}/>
      </Routes>
    </AppLayout>
  )
}

export default Title

import AppLayout from '/src/components/layouts/app_layout'
import TransfersTable from '/src/components/transfers/table'
import { useState } from 'react'

function Deliveries () {
  const [dynamicTitle, setDynamicTitle] =  useState('Transfers')

  return (
    <AppLayout
      contentClassNames={'p-8'}
      header={dynamicTitle}>
      <TransfersTable updateTitle={setDynamicTitle}/>
    </AppLayout>
  )
}

export default Deliveries

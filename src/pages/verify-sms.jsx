import FullLogo from '/src/components/logos/full_logo'
import AuthCard from '/src/components/auth_card'
import AuthSessionStatus from '/src/components/auth_session_status'
import GuestLayout from '/src/components/layouts/guest_layout'
import { Input, Button } from '@bitcine/cinesend-theme'
import { useAuth } from '/src/hooks/auth'
import { useState } from 'react'
import { Link } from 'react-router-dom'
import ErrorMessages from '/src/components/error_messages'

const VerifySms = () => {

  const { confirmSmsTwoFactorChallenge, sendSmsTwoFactorCode } = useAuth({
    middleware: 'auth'
  })

  const [sentCode, setSentCode] = useState(false)
  const [code, setCode] = useState('')
  const [errors, setErrors] = useState([])
  const [pending, setPending] = useState(false)
  const [status, setStatus] = useState([])

  const submitForm = event => {
    event.preventDefault()
    confirmSmsTwoFactorChallenge({ code, setErrors, setStatus, setPending })
  }

  const sendCode = () => {
    setSentCode(true)
    sendSmsTwoFactorCode({ setErrors, setStatus, setPending })
  }

  return (
    <GuestLayout>
      <AuthCard
        logo={
          <Link to="/">
            <FullLogo className="h-20 w-auto"/>
          </Link>
        }>
        {/* Session Status */}
        <AuthSessionStatus className="mb-4" status={status}/>
        {sentCode &&
          <form onSubmit={submitForm} className="space-y-4 text-sm">
            <div>Your account has SMS-based 2FA enabled.
              Please paste the code sent to your phone below.</div>
            <Input
              label="SMS Code"
              value={code}
              onChange={event => setCode(event.target.value)}
            />
            <div className="flex justify-end">
              <Button disabled={!code || pending}>
                Verify SMS Code
              </Button>
            </div>
          </form>
        }
        {!sentCode &&
          <div className='flex flex-col space-y-4 text-sm'>
            <div>Your account has SMS-based 2FA enabled.
              To sign in, click the button below to send a unique code to your phone.</div>
            <div className="flex justify-end">
              <Button onClick={sendCode} disabled={pending}>
                Send SMS Code
              </Button>
            </div>
          </div>
        }
        <ErrorMessages errors={errors}/>
      </AuthCard>
    </GuestLayout>
  )
}

export default VerifySms

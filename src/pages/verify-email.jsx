import FullLogo from '/src/components/logos/full_logo'
import AuthCard from '/src/components/auth_card'
import AuthSessionStatus from '/src/components/auth_session_status'
import GuestLayout from '/src/components/layouts/guest_layout'
import { Button } from '@bitcine/cinesend-theme'
import { useAuth } from '/src/hooks/auth'
import { useState } from 'react'
import { Link } from 'react-router-dom'

const VerifyEmail = () => {

  const { resendEmailVerification } = useAuth({
    middleware: 'guest'
  })

  const [status, setStatus] = useState([])

  const submitForm = async event => {
    event.preventDefault()
    resendEmailVerification({ setStatus })
  }

  return (
    <GuestLayout>
      <AuthCard
        logo={
          <Link to="/">
            <FullLogo className="h-20 w-auto"/>
          </Link>
        }>
        {/* Session Status */}
        <AuthSessionStatus className="mb-4" status={status}/>
        <p>You need to verify your email.</p>
        <form onSubmit={submitForm} className="space-y-4">
          <div className="flex items-center justify-between">
            <Button type="warning">
              Resend Email
            </Button>
          </div>
        </form>
      </AuthCard>
    </GuestLayout>
  )
}

export default VerifyEmail

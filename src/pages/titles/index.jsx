import AppLayout from '/src/components/layouts/app_layout'
import TitlesTable from '/src/components/titles/table'
import CreateTitle from '/src/components/titles/create_title'
import { useState } from 'react'

function Titles () {
  const [dynamicTitle, setDynamicTitle] =  useState('Titles')
  return (
    <AppLayout
      contentClassNames={'p-8'}
      header={dynamicTitle} buttons={[<CreateTitle key={1}/>]}>
      <TitlesTable updateTitle={setDynamicTitle}/>
    </AppLayout>

  )
}

export default Titles

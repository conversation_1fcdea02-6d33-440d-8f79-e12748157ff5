import AppLayout from '/src/components/layouts/app_layout'
import SitesTable from '/src/components/sites/table'
import CreateSite from '/src/components/sites/create_site'
import { useState } from 'react'

function Sites () {
  const [dynamicTitle, setDynamicTitle] =  useState('Sites')

  return (
    <AppLayout
      contentClassNames={'p-8'}
      header={dynamicTitle} buttons={[<CreateSite/>]}>
      <SitesTable updateTitle={setDynamicTitle}/>
    </AppLayout>
  )
}

export default Sites

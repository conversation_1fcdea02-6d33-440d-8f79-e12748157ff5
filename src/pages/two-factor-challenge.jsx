import FullLogo from '/src/components/logos/full_logo'
import AuthCard from '/src/components/auth_card'
import AuthSessionStatus from '/src/components/auth_session_status'
import AuthValidationErrors from '/src/components/auth_validation_errors'
import GuestLayout from '/src/components/layouts/guest_layout'
import { Input, Button } from '@bitcine/cinesend-theme'
import { useAuth } from '/src/hooks/auth'
import { useState } from 'react'
import { Link } from 'react-router-dom'

const TwoFactorChallenge = () => {

  const { confirmTwoFactorChallenge } = useAuth({
    middleware: 'guest'
  })

  const [code, setCode] = useState('')
  const [recoveryCode, setRecoveryCode] = useState('')
  const [errors, setErrors] = useState([])
  const [pending, setPending] = useState(false)
  const [status, setStatus] = useState(null)

  const submitForm = async event => {
    event.preventDefault()
    confirmTwoFactorChallenge({ code, recoveryCode, setErrors, setStatus, setPending })
  }

  return (
    <GuestLayout>
      <AuthCard
        logo={
          <Link to="/">
            <FullLogo className="h-20 w-auto" />
          </Link>
        }>
        {/* Session Status */}
        <AuthSessionStatus className="mb-4" status={status} />
        {/* Validation Errors */}
        <AuthValidationErrors className="mb-4" errors={errors} />
        <form onSubmit={submitForm} className='space-y-4'>
          <Input
            label="Code"
            value={code}
            onChange={event => setCode(event.target.value)}
          />
          <Input
            label="Or use a one-time recovery code"
            value={recoveryCode}
            onChange={event => setRecoveryCode(event.target.value)}
          />
          <div className="flex items-center justify-between">
            <Button type='warning' disabled={(!code && !recoveryCode) || pending}>
              Verify Code
            </Button>
          </div>
        </form>
      </AuthCard>
    </GuestLayout>
  )
}

export default TwoFactorChallenge

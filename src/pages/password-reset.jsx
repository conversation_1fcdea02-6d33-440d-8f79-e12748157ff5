import FullLogo from '/src/components/logos/full_logo'
import AuthCard from '/src/components/auth_card'
import AuthSessionStatus from '/src/components/auth_session_status'
import AuthValidationErrors from '/src/components/auth_validation_errors'
import GuestLayout from '/src/components/layouts/guest_layout'
import { Input, Button } from '@bitcine/cinesend-theme'
import { useAuth } from '/src/hooks/auth'
import { useState } from 'react'
import {Link, useParams} from 'react-router-dom'

const PasswordReset = () => {
  const params = useParams()
  const { resetPassword } = useAuth({ middleware: 'guest' })

  const [email, setEmail] = useState(params.email)
  const [password, setPassword] = useState('')
  const [password_confirmation, setPasswordConfirmation] = useState('')
  const [errors, setErrors] = useState([])
  const [status, setStatus] = useState(null)

  const submitForm = event => {
    event.preventDefault()
    resetPassword({
      email,
      password,
      password_confirmation,
      setErrors,
      setStatus
    })
  }

  return (
    <GuestLayout>
      <AuthCard
        logo={
          <Link to="/">
            <FullLogo className="h-20 w-auto" />
          </Link>
        }>
        {/* Session Status */}
        <div className="mb-4 text-sm text-gray-600">
          Please enter a new password below. The requirements are:
          <ol className={'list-decimal mx-8 my-2 font-bold'}>
            <li>1 Upper case character</li>
            <li>1 Numeric character</li>
            <li>1 Special character</li>
            <li>8 characters minimum.</li>
          </ol>
          For best results, use a password manager with randomly generated strong passwords.
        </div>
        <AuthSessionStatus className="mb-4" status={status} />
        {/* Validation Errors */}
        <AuthValidationErrors className="mb-4" errors={errors} />
        <form onSubmit={submitForm}>
          {/* Email Address */}
          <div>
            <Input
              disabled={true}
              label='Email'
              id="email"
              type="email"
              value={params.email}
              disalbed={true}
            />
          </div>
          {/* Password */}
          <div className="mt-4">
            <Input
              label='Password'
              id="password"
              type="password"
              value={password}
              className="block mt-1 w-full"
              onChange={event => setPassword(event.target.value)}
              required
              autofocus
            />
          </div>
          {/* Confirm Password */}
          <div className="mt-4">
            <Input
              label='Confirm password'
              id="password_confirmation"
              type="password"
              value={password_confirmation}
              className="block mt-1 w-full"
              onChange={event =>
                setPasswordConfirmation(event.target.value)
              }
              required
            />
          </div>
          <div className="flex items-center justify-end mt-4">
            <Button disabled={!password || !password_confirmation}>Reset Password</Button>
          </div>
        </form>
      </AuthCard>
    </GuestLayout>
  )
}

export default PasswordReset

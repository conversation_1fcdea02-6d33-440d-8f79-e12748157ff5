import { useQuery, useQueryClient } from '@tanstack/react-query'
import AppLayout from '/src/components/layouts/app_layout'
import Download from '/src/components/reports/downloader'
import Picker from '/src/components/reports/picker'

function Reports () {

  const queryClient = useQueryClient()
  const { data: downloads } = useQuery({ queryKey: ['/api/reports']})
  const mutate = () => queryClient.invalidateQueries({ queryKey: ['/api/reports'] })

  return (
    <AppLayout
      contentClassNames={'p-8'}
      header={'Reports'}>
      <Picker onSubmit={() => { mutate() }}/>
      {downloads && <Download downloads={downloads?.data?.data}/>}
    </AppLayout>
  )
}

export default Reports

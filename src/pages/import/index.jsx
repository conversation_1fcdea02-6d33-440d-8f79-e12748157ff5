import AppLayout from '/src/components/layouts/app_layout'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useParams } from 'react-router-dom'
import ImportDetails from '/src/components/imports/import_details'
import ImportRecordsTable from '/src/components/imports/import_records_table'
import { useQuery } from '@tanstack/react-query'

function ImportRecord () {

  const { importID } = useParams()

  const apiUrl = `/api/import-bookings/${importID}`

  const { data } = useQuery({ queryKey: [apiUrl], refetchInterval: 10000 })

  const importRecord = data?.data

  const importName = importRecord ? `${importRecord?.origin_name}` : null

  return (
    <AppLayout
      contentClassNames={'p-8'}
      breadcrumbs={[{ to: '/imports', text: 'Imports' }, { text: importName }]}
      pending={!importRecord}
      tabs={
        [
          { name: 'Import Details', to: 'details' },
          { name: `Pending Records (${importRecord?.pending_count})`, to: 'pending' },
          { name: `Matched Records (${importRecord?.matched_count})`, to: 'matched' },
          { name: `Bookings Created (${importRecord?.booked_count})`, to: 'booked' }
        ]
      }>
      <Routes>
        <Route exact path="" element={<Navigate to="details" replace/>}/>
        <Route path="details" element={<ImportDetails importRecord={importRecord}/>}/>
        <Route path="pending"
          element={<ImportRecordsTable apiUrl={`/api/import-bookings/${importRecord?.id}/pending`}/>}/>
        <Route path="matched"
          element={<ImportRecordsTable apiUrl={`/api/import-bookings/${importRecord?.id}/matched`}/>}/>
        <Route path="booked"
          element={<ImportRecordsTable apiUrl={`/api/import-bookings/${importRecord?.id}/booked`}/>}/>
      </Routes>
    </AppLayout>
  )
}

export default ImportRecord

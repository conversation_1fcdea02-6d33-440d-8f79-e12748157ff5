import FullLogo from '/src/components/logos/full_logo'
import AuthCard from '/src/components/auth_card'
import AuthSessionStatus from '/src/components/auth_session_status'
import AuthValidationErrors from '/src/components/auth_validation_errors'
import GuestLayout from '/src/components/layouts/guest_layout'
import { Input, Button } from '@bitcine/cinesend-theme'
import { useAuth } from '/src/hooks/auth'
import { useState } from 'react'
import { Link, NavLink, useParams } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'

const Login = () => {

  let { reset } = useParams()

  const apiUrl = '/api/request-access'

  const { data, isLoading } = useQuery({ 
    queryKey: [apiUrl], 
    refetchOnMount: false, 
    refetchOnReconnect: false, 
    refetchOnWindowFocus: false 
  })

  const { login } = useAuth({
    middleware: 'guest'
  })

  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [errors, setErrors] = useState([])
  const [pending, setPending] = useState(false)
  const [status, setStatus] = useState(
    reset ? 'Your password has been reset. Please sign in with your new password.' : null)

  const submitForm = async event => {
    event.preventDefault()
    login({ email, password, setErrors, setStatus, setPending })
  }

  return (
    <GuestLayout>
      <AuthCard
        logo={
          <Link to="/">
            <FullLogo className="h-20 w-auto" />
          </Link>
        }>
        {/* Session Status */}
        <AuthSessionStatus className="mb-4" status={status} />
        {/* Validation Errors */}
        <AuthValidationErrors className="mb-4" errors={errors} />
        <form onSubmit={submitForm} className="space-y-4">
          <Input
            label="Email"
            value={email}
            onChange={event => setEmail(event.target.value)}
          />
          <Input
            label="Password"
            type="password"
            value={password}
            onChange={event => setPassword(event.target.value)}
          />
          <div className="flex items-center justify-between">
            <div>
              <NavLink
                to="/forgot-password"
                className="underline text-sm text-gray-600 hover:text-gray-900"
              >
                Forgot your password?
              </NavLink>
              {!isLoading &&
              <a
                href={`mailto:${data.email}?subject=Portal%20Access%20Request&body=Full%20Name%3A%0AEmail%20Address%3A%0ACompany%3A%0ARole%3A`}
                className="block"
              >
                <p className="underline text-sm cursor-pointer text-gray-600 hover:text-gray-900">
                  Request Access
                </p>
              </a>}

            </div>
            <Button type="warning" disabled={!email || !password || pending}>
              Log In
            </Button>
          </div>
        </form>
      </AuthCard>
    </GuestLayout>
  )
}

export default Login

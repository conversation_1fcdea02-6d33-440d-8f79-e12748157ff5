import { Routes, Route, Navigate } from 'react-router-dom'
import AppLayout from '/src/components/layouts/app_layout'
import { useParams } from 'react-router-dom'
import Details from '/src/components/users/details'
import Activity from '/src/components/users/activity'
import ApiTokens from '/src/components/users/api_tokens'
import { useQuery } from '@tanstack/react-query'

function User () {

  const { userID } = useParams()

  const apiUrl = `/api/users/${userID}`
  const { data } = useQuery({ queryKey: [apiUrl] })
  let user = data?.data

  return (
    <AppLayout
      contentClassNames={'p-8'}
      header={user?.name}
      breadcrumbs={[{ to: '/users', text: 'Users' }, { text: user?.name }]}
      pending={!user}
      tabs={
        [{ key: 'details' }, { key: 'activity' }, { key: 'api-tokens', name: 'API Tokens' }].map(opt => ({
          name: opt.name ?? opt.key,
          to: `/users/${userID}/${opt.key}`
        }))
      }>
      <Routes>
        <Route exact path="" element={<Navigate to="details" replace/>} />
        <Route path="details" element={<Details user={user} mutationKey={apiUrl}/>} />
        <Route path="api-tokens" element={<ApiTokens user={user}/>} />
        <Route path="activity" element={<Activity user={user}/>} />
      </Routes>
    </AppLayout>
  )
}

export default User

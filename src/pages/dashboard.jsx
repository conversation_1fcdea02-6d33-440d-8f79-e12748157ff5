import { useAuth } from '/src/hooks/auth'
import AdminDashboard from '/src/components/dashboards/admin'
import SiteDashboard from '/src/components/dashboards/site'
import StudioDashboard from '/src/components/dashboards/studio'
import VendorDashboard from '/src/components/dashboards/vendor'
import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'

function Dashboard() {
  const { user } = useAuth()
  const navigate = useNavigate()

  useEffect(() => {
    if (user.needs_password) {
      navigate('/settings')
    }
  }, [user])

  return (
    <>
      {
        {
          admin: <AdminDashboard />,
          vendor: <VendorDashboard />,
          exhibitor: <SiteDashboard />, // leave as is (from data) for now.
          studio: <StudioDashboard />,
        }[user.organization_type]
      }
    </>
  )
}

export default Dashboard

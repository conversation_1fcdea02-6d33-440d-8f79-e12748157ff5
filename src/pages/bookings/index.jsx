import AppLayout from '/src/components/layouts/app_layout'
import BookingsTable from '/src/components/bookings/table'
import CreateBooking from '/src/components/bookings/create_booking'
import { Button } from '@bitcine/cinesend-theme'
import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '/src/hooks/auth'

function Bookings () {
  const navigate = useNavigate()
  const [dynamicTitle, setDynamicTitle] = useState('Bookings')
  const { checkPermission } = useAuth()
  return (
    <AppLayout
      contentClassNames={'p-8'}
      header={dynamicTitle} buttons={[
        (checkPermission('view-imports') ? <Button onClick={() => navigate('/imports')}
          icon="upload_file" secondary>View Imports</Button> : null),
        (checkPermission('create-bookings') ? <CreateBooking/> : null)
      ]}>
      <BookingsTable updateTitle={setDynamicTitle}/>
    </AppLayout>
  )
}

export default Bookings

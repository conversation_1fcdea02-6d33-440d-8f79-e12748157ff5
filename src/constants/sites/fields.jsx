import { convertToFromNow, convertToLocalDateTime } from '/src/helpers/convert_date'
import { states } from './states'

const buildFields = ({
  primary,
  secondary,
  componentType = 'input',
  disabled = false,
  prefix = '',
  middleFix = ''
}) => {
  let fields = []
  primary.forEach(type => {
    secondary.forEach(key => {
      let label = `${prefix}${type}${middleFix}${key}`
      fields.push({
        label,
        key: label.toLowerCase().replaceAll(' ', '_'),
        type: componentType,
        disabled
      })
    })
  })
  return fields
}

const statuses = [
  { label: 'Pending Installation', value: 'pending' },
  { label: 'Faulted', value: 'fault' },
  { label: 'Online', value: 'online' }
]

const FIELDS = {
  status: [
    {
      title: 'Site Status',
      fields: [
        {
          label: 'Status',
          key: 'status',
          type: 'select',
          options: statuses
        },
        {
          label: 'Last Phoned Home',
          key: 'media_manager_status_updated_at',
          render: (value) => convertToLocalDateTime(value),
          type: 'input',
          disabled: true
        }
      ]
    }
  ],
  general: [
    {
      title: 'Location',
      layout: 'grid grid-flow-row grid-dense grid-cols-3 gap-4 pr-8',
      fields: [
        {
          label: 'Site Name',
          key: 'name',
          type: 'input'
        },
        {
          label: 'Circuit',
          key: 'circuit',
          type: 'input'
        },
        {
          label: 'Address',
          key: 'address',
          type: 'input'
        },
        {
          label: 'City',
          key: 'city',
          type: 'input'
        },
        {
          label: 'State',
          key: 'state',
          type: 'select',
          options: states,
          width: 'col-span-2'
        },
        {
          label: 'ZIP',
          key: 'zip',
          type: 'input',
          width: 'col-span-1'
        }
      ]
    },
    {
      title: 'Site IDs',
      layout: 'grid grid-flow-row grid-dense grid-cols-2 gap-4',
      fields: [
        {
          label: 'TCN',
          key: 'tcn',
          type: 'input',
          width: 'col-span-1'
        },
        {
          label: 'Secondary TCN (optional)',
          key: 'secondary_tcn',
          type: 'input',
          width: 'col-span-1'
        },
        {
          label: 'Rentrack',
          key: 'rentrack',
          type: 'input',
          width: 'col-span-1'
        },
        {
          label: 'DCDC / NetSuite Customer Number',
          key: 'sage_customer_number',
          type: 'input',
          width: 'col-span-1'
        },
        {
          label: 'Disney Site ID',
          key: 'disney_site_id',
          type: 'input',
          width: 'col-span-1'
        },
        {
          label: 'Paramount Theatre ID',
          key: 'paramount_theatre_id',
          type: 'input',
          width: 'col-span-1'
        },
        {
          label: 'Lionsgate Theatre ID',
          key: 'lionsgate_theatre_id',
          type: 'input',
          width: 'col-span-1'
        },
        {
          label: 'DCHub Cinema ID',
          key: 'dchub_cinema_id',
          type: 'input', width: 'col-span-1'

        },
        {
          label: 'Universal Site ID',
          key: 'universal_site_id',
          type: 'input',
          width: 'col-span-1'
        },
        {
          label: 'Sony Site ID',
          key: 'sony_site_id',
          type: 'input',
          width: 'col-span-1'
        }
      ]
    }
  ],
  device: [
    {
      title: 'Device',
      fields: [
        {
          label: 'CS1 Serial Number',
          key: 'serial_number',
          type: 'input',
          disabled: true
        },
        {
          label: 'Status',
          key: 'status',
          type: 'input',
          disabled: true
        },
        {
          label: 'Uptime',
          key: 'started_at',
          type: 'input',
          render: value => convertToFromNow(value),
          disabled: true
        },
        {
          label: 'SSH port',
          key: 'ssh_port',
          type: 'input'
        },
        {
          label: 'Web port',
          key: 'web_port',
          type: 'input'
        }
      ]
    }
  ],
  cinema_pro: [
    {
      title: 'Cinema Pro',
      layout: 'grid grid-flow-row grid-dense grid-cols-4 gap-4',
      fields: [
        {
          label: 'Site Name',
          key: 'receive_site_name',
          type: 'input',
          disabled: true,
          width: 'col-span-2'
        },
        {
          label: 'Serial Number',
          key: 'serial_number',
          type: 'input',
          disabled: true,
          width: 'col-span-1'
        },
        {
          label: 'Site ID',
          key: 'site_id',
          type: 'input',
          disabled: true,
          width: 'col-span-1'
        },
        {
          label: 'Raid Type',
          key: 'raid_type',
          type: 'input',
          disabled: true,
          width: 'col-span-1'
        },
        {
          label: 'Raid State',
          key: 'raid_state',
          type: 'input',
          disabled: true,
          width: 'col-span-1'
        },
        {
          label: 'Raid Size',
          key: 'raid_size',
          type: 'input',
          disabled: true,
          width: 'col-span-1'
        },
        {
          label: 'Raid Percent',
          key: 'raid_percent_used',
          type: 'input',
          disabled: true,
          width: 'col-span-1'
        },
        {
          label: 'Internal IP',
          key: 'ip_address',
          type: 'input',
          disabled: true,
          width: 'col-span-2'
        }
      ]
    }
  ]

}

export default FIELDS

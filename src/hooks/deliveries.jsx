import React from 'react'
import axios from '/src/lib/axios'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { DeliveryContext } from '/src/contexts/delivery'
import { useSnackbar } from './snackbar'

export const useDeliveries = () => {
  const { addMessage } = useSnackbar()
  const queryClient = useQueryClient()
  const [, dispatch] = React.useContext(DeliveryContext)

  const showDelivery = delivery => {
    dispatch({ delivery })
  }

  const createDelivery = useMutation({
    mutationFn: props => axios.post('/api/deliveries', props),
    onSuccess: (res, variables) => {
      addMessage('Delivery created!')
      if (variables.onComplete) variables.onComplete(res)
    },
    onError: (error, variables) => {
      if (error.response?.status === 422) {
        if (variables.setErrors) {
          variables.setErrors(Object.values(error.response.data.errors).flat())
        }
      } else {
        throw error
      }
    }
  })

  const updateDelivery = useMutation({
    mutationFn: ({ deliveryID, ...props }) => axios.put(`/api/deliveries/${deliveryID}`, props),
    onSuccess: (res, variables) => {
      addMessage('Delivery updated!')
      showDelivery(res.data?.data)
      queryClient.invalidateQueries({ queryKey: [`/api/bookings/${res.data?.booking_id}/deliveries`] })
      if (variables.onComplete) variables.onComplete(res)
    },
    onError: (error, variables) => {
      if (error.response?.status === 422) {
        if (variables.setErrors) {
          variables.setErrors(Object.values(error.response.data.errors).flat())
        }
      } else {
        throw error
      }
    }
  })

  const deleteDelivery = useMutation({
    mutationFn: deliveryID => axios.delete(`/api/deliveries/${deliveryID}`),
    onSuccess: () => {
      addMessage('Delivery deleted!')
    },
    onError: () => {
      addMessage('Could not delete this delivery.', 'error')
    }
  })

  const sendDelivery = useMutation({
    mutationFn: delivery => axios.post(`/api/transfers/${delivery.id}/send-download-job`),
    onSuccess: (_, variables) => {
      addMessage('Transfer request dispatched!')
      if (variables.onComplete) variables.onComplete()
    },
    onError: (error, variables) => {
      if (error.response?.status === 422) {
        addMessage(Object.values(error.response.data.errors).flat(), 'error', null, 5000)
      }
      if (variables.onComplete) variables.onComplete()
    }
  })

  return {
    createDelivery: createDelivery.mutate,
    updateDelivery: updateDelivery.mutate,
    deleteDelivery: deleteDelivery.mutate,
    sendDelivery: sendDelivery.mutate,
    showDelivery
  }
}

import axios from '/src/lib/axios'
import { useNavigate } from 'react-router-dom'
import { useSnackbar } from './snackbar'

export const useOrganizations = () => {
  let navigate = useNavigate()
  let { addMessage } = useSnackbar()

  const createOrganization = async ({ setErrors, ...props }) => {
    setErrors([])
    axios.post('/api/organizations', props).then(res => {
      navigate(`/organizations/${res.data.data.id}`)
      addMessage('Organization created!')
    }).catch(error => {
      if (error.response.status !== 422) {
        throw error
      }
      setErrors(Object.values(error.response.data.errors).flat())
    })
  }

  const updateOrganization = async ({ setErrors, onComplete, organizationID, ...props }) => {
    setErrors([])
    axios.put(`/api/organizations/${organizationID}`, props).then(res => {
      onComplete(res)
      addMessage('Organization updated!')
    }).catch(error => {
      if (error.response.status !== 422) {
        throw error
      }
      setErrors(Object.values(error.response.data.errors).flat())
    })
  }

  const deleteOrganization = async ({ setErrors, organizationID }) => {
    axios.delete(`/api/organizations/${organizationID}`).then(() => {
      addMessage('Organization deleted!')
    }).catch(error => {
      if (error.response.status !== 422) {
        throw error
      }
      setErrors(Object.values(error.response.data.errors).flat())
    })
  }

  const updateWebhook = async ({ setErrors, organizationID, id, onComplete, ...props }) => {
    axios.put(`/api/organizations/${organizationID}/webhooks/${id}`, props).then(res => {
      onComplete(res)
      addMessage('Webhook updated!')
    }).catch(error => {
      if (error.response.status !== 422) {
        throw error
      }
      setErrors(Object.values(error.response.data.errors).flat())
    })
  }

  const createWebhook = async ({ setErrors, organizationID, onComplete, ...props }) => {
    axios.post(`/api/organizations/${organizationID}/webhooks`, props).then(res => {
      onComplete(res)
      addMessage('Webhook created!')
    }).catch(error => {
      if (error.response.status !== 422) {
        throw error
      }
      setErrors(Object.values(error.response.data.errors).flat())
    })
  }
  const deleteWebhook = async ({ setErrors, organizationID, id, onComplete }) => {
    axios.delete(`/api/organizations/${organizationID}/webhooks/${id}`).then(() => {
      addMessage('Webhook deleted!')
      onComplete()
    }).catch(error => {
      if (error.response.status !== 422) {
        throw error
      }
      setErrors(Object.values(error.response.data.errors).flat())
    })
  }

  return {
    createOrganization,
    updateOrganization,
    deleteOrganization,
    updateWebhook,
    createWebhook,
    deleteWebhook
  }
}

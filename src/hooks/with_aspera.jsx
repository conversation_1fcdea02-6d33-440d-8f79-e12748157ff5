import { initAspera, asperaWeb, asperaInstaller, config } from '/src/components/aspera/aspera'
import axios from '/src/lib/axios'

export const withAspera = () => {

  const createUpload = async ({ setErrors, ...props }) => {
    setErrors([])
    axios.post('/api/aspera/upload', props).then(res => {
      // post to the upload to create, then get the transfer_spec
      const connectSettings = {
        allow_dialogs: 'no',
        use_absolute_destination_path: false
      }
      // and start.
      asperaWeb.startTransfer(res.data.transfer_spec, connectSettings)
      //?

    }).catch(error => {
      if (error.response.status !== 422) {
        throw error
      }
      setErrors(Object.values(error.response.data.errors).flat())
    })
  }

  const createPublicUpload = async ({ url, setErrors, onComplete, ...props }) => {
    setErrors([])
    axios.post(url, props).then(res => {
      const connectSettings = {
        allow_dialogs: 'no',
        use_absolute_destination_path: false
      }
      asperaWeb.startTransfer(res.data?.aspera_transfer?.transfer_spec, connectSettings)
    }).catch(error => {
      setErrors(Object.values(error.response.data.errors).flat())
    }).finally(() => {
      if (onComplete instanceof Function) {
        onComplete()
      }
    })
  }

  const createDownload = async ({ setErrors, ...props }) => {
    setErrors([])
    axios.post('/api/aspera/download', props).then(res => {
      const opts = {
        title: 'Select Save Location',
        suggestedName: res.data.transfer_specs[0].transfer_spec.tags['cinesend'].name
      }
      const callBacks = {
        success: path => {
          if (!path.dataTransfer.files.length) {
            asperaWeb.stop()
          }
          else {
            const transferSpec = {
              ...res.data.transfer_specs[0].transfer_spec,
              destination_root: path.dataTransfer.files[0].name,
              authentication: 'token'
            }
            const connectSettings = {
              allow_dialogs: 'no',
              use_absolute_destination_path: true
            }
            asperaWeb.startTransfer(transferSpec, connectSettings)
          }
        }
      }
      asperaWeb.showSaveFileDialog(callBacks, opts)
    }).catch(error => {
      throw error
    })
  }

  if (!asperaWeb) {
    initAspera({
      sdkLocation: '//d3gcli72yxqn2z.cloudfront.net/connect/v4',
      minVersion: '3.8.0'
    })
  }

  return ({
    initAspera,
    createUpload,
    createDownload,
    createPublicUpload,
    asperaWeb: asperaWeb,
    asperaInstaller: asperaInstaller,
    config: config
  })

}

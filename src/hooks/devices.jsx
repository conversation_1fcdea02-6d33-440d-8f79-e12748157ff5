import axios from '/src/lib/axios'
import { useNavigate } from 'react-router-dom'
import { useSnackbar } from './snackbar'

export const useDevices = () => {
  let navigate = useNavigate()
  let { addMessage } = useSnackbar()

  const createDevice = async ({ setErrors, ...props }) => {
    setErrors([])
    axios.post('/api/media-managers', props)
      .then(res => {
        addMessage('Device created!')
        navigate(`/Devices/${res.data.data.id}`)
      })
      .catch(error => {
        if (error.response.status !== 422) throw error
        setErrors(Object.values(error.response.data.errors).flat())
      })
  }

  const updateDevice = async ({ setErrors, onComplete, deviceID, ...props }) => {
    setErrors([])
    axios.put(`/api/media-managers/${deviceID}`, props)
      .then(() => addMessage('Device updated!'))
      .catch(error => {
        if (error.response.status !== 422) throw error
        addMessage(error.response.data.message, 'error', 'alert', 5000)
      })
  }

  const deleteDevice = async ({ setErrors, deviceID }) => {
    axios.delete(`/api/media-managers/${deviceID}`)
      .then(() => {
        addMessage('Device deleted!')
      })
      .catch(error => {
        if (error.response.status !== 422) throw error
        setErrors(Object.values(error.response.data.errors).flat())
      })
  }

  return {
    createDevice,
    updateDevice,
    deleteDevice
  }
}

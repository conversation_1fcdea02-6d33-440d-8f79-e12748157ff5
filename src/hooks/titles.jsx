import axios from '/src/lib/axios'
import { useNavigate } from 'react-router-dom'
import { useSnackbar } from './snackbar'
import { useMutation, useQueryClient } from '@tanstack/react-query'

export const useTitles = () => {
  const navigate = useNavigate()
  const { addMessage } = useSnackbar()
  const queryClient = useQueryClient()

  const createTitle = useMutation({
    mutationFn: (props) => axios.post('/api/titles', props),
    onSuccess: (res) => {
      addMessage('Title created!')
      navigate(`/titles/${res.data.data.id}`)
    },
    onError: (error, props) => {
      if (error.response.status === 422) {
        props.setErrors(Object.values(error.response.data.errors).flat())
      } else {
        throw error
      }
    }
  })

  const updateTitle = useMutation({
    mutationFn: ({ titleID, ...props }) => axios.put(`/api/titles/${titleID}`, props),
    onSuccess: (res, variables) => {
      variables.onComplete(res)
      addMessage('Title updated!')
    },
    onError: (error) => {
      addMessage(error.response.data.message, 'error')
      if (error.response.status !== 422) throw error
    }
  })

  const deleteTitle = useMutation({
    mutationFn: ({ titleID }) => axios.delete(`/api/titles/${titleID}`),
    onSuccess: () => {
      addMessage('Title deleted!')
    },
    onError: () => {
      addMessage('Could not delete Title.', 'error')
    }
  })

  const createVersion = useMutation({
    mutationFn: ({ titleID, ...props }) => axios.post(`/api/titles/${titleID}/versions`, props),
    onSuccess: (res, variables) => {
      addMessage('Content record created! Aspera is now launching.')
      variables.onComplete(res.data.data.id)
    },
    onError: (error) => {
      addMessage(error.response.data.message, 'error')
      if (error.response.status !== 422) throw error
    }
  })

  const updateVersion = useMutation({
    mutationFn: ({ versionID, ...props }) => axios.put(`/api/versions/${versionID}`, props),
    onSuccess: (res, variables) => {
      addMessage('Content updated!')
      variables.onComplete(res)
    },
    onError: (error) => {
      addMessage(error.response.data.message, 'error')
      if (error.response.status !== 422) throw error
    }
  })

  const deleteVersion = useMutation({
    mutationFn: ({ versionID }) => axios.delete(`/api/versions/${versionID}`),
    onSuccess: (res, variables) => {
      addMessage('Content deleted!')
    },
    onError: (error) => {
      addMessage(error.response.data.message, 'error')
      if (error.response.status !== 422) throw error
    }
  })

  const linkOrphan = useMutation({
    mutationFn: ({ titleID, asperaTransferID }) => axios.post(`/api/titles/orphans/${titleID}/${asperaTransferID}`),
    onSuccess: (res, variables) => {
      addMessage('Upload linked to Title. Please wait while the DCPs are checked.')
      queryClient.invalidateQueries({ queryKey: [variables.invalidationKey] })
    },
    onError: (error) => {
      addMessage(error.response.data.message, 'error')
      if (error.response.status !== 422) throw error
    }
  })

  const createRelease = useMutation({
    mutationFn: ({ titleID, ...props }) => axios.post(`/api/titles/${titleID}/releases`, props),
    onSuccess: (res, variables) => {
      addMessage('Release Created.')
      variables.onComplete(res.data.data.id)
    },
    onError: (error) => {
      addMessage(error.response.data.message, 'error')
      if (error.response.status !== 422) throw error
    }
  })

  const updateRelease = useMutation({
    mutationFn: ({ titleID, releaseID, ...props }) =>
      axios.put(`/api/titles/${titleID}/releases/${releaseID}`, props),
    onSuccess: (res, variables) => {
      addMessage('Release Updated.')
      variables.onComplete(res.data.data.id)
    },
    onError: (error) => {
      addMessage(error.response.data.message, 'error')
      if (error.response.status !== 422) throw error
    }
  })

  const deleteRelease = useMutation({
    mutationFn: ({ titleID, releaseID }) => axios.delete(`/api/titles/${titleID}/releases/${releaseID}`),
    onSuccess: (res, variables) => {
      addMessage('Release Deleted.')
      variables.onComplete(res.data.data.id)
    },
    onError: (error) => {
      addMessage(error.response.data.message, 'error')
      if (error.response.status !== 422) throw error
    }
  })

  const prepareSatelliteRelease = useMutation({
    mutationFn: ({ titleID, releaseID }) => axios.post(`/api/titles/${titleID}/releases/${releaseID}/trigger-fazzt`, {}),
    onSuccess: (res, variables) => {
      addMessage('Satellite Package Being Prepared.')
      variables.onComplete(res.data.data.id)
    },
    onError: (error) => {
      addMessage(error.response.data.message, 'error')
      if (error.response.status !== 422) throw error
    }
  })

  return {
    createTitle: createTitle.mutate,
    updateTitle: updateTitle.mutate,
    deleteTitle: deleteTitle.mutate,
    createVersion: createVersion.mutate,
    updateVersion: updateVersion.mutate,
    deleteVersion: deleteVersion.mutate,
    createRelease: createRelease.mutate,
    updateRelease: updateRelease.mutate,
    deleteRelease: deleteRelease.mutate,
    prepareSatelliteRelease: prepareSatelliteRelease.mutate,
    linkOrphan: linkOrphan.mutate
  }
}

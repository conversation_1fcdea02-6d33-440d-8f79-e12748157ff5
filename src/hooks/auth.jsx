import axios from '/src/lib/axios'
import { useNavigate, useParams } from 'react-router-dom'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

const queryKey = '/api/user'

export const useAuth = ({ middleware } = {}) => {
  const navigate = useNavigate()
  const params = useParams()
  const queryClient = useQueryClient()

  const { data: user, error, isFetched } = useQuery({ 
    queryKey: [queryKey],
    retry: false,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: true,
    queryFn: async () => {
      try {
        const res = await axios.get(queryKey)
        if (res.data.needs_password) {
          navigate('/settings')
        }
        return res.data
      }
      catch(err) {
        switch (err.response.status) {
          case 409:
            navigate('/verify')
            break
          default:
            throw err
        }
        
      }
    }
  })

  const invalidateUserQuery = (redirect<PERSON><PERSON> = null) => {
    queryClient.invalidateQueries({ queryKey: [queryKey] })
    if (redirectKey) navigate(redirectKey)
  }

  const register = useMutation({
    mutationFn: async ({ setErrors, ...props }) => {
      setErrors?.([])
      try {
        return await axios.post('/register', props)
      } catch (error) {
        throw error
      }
    },
    onError: (error, variables) => {
      if (error.response?.status === 422) {
        variables.setErrors?.(Object.values(error.response.data.errors || {}).flat())
      } else {
        throw error
      }
    },
    onSuccess: invalidateUserQuery
  })

  const login = useMutation({
    mutationFn: async ({ setErrors, ...props }) => {
      setErrors?.([])
      try {
        return await axios.post('/login', props)
      } catch (error) {
        throw error
      }
    },
    onError: (error, variables) => {
      if (error.response?.status === 422) {
        variables.setErrors?.(Object.values(error.response.data.errors || {}).flat())
      } else {
        throw error
      }
    },
    onSuccess: (response) => {

      const { two_factor, needs_password } = response.data
      if (two_factor) navigate('/two-factor-challenge')
      else if (needs_password) navigate('/settings')
      else navigate('/dashboard')
      localStorage.clear()
      invalidateUserQuery()
    }
  })

  const forgotPassword = useMutation({
    mutationFn: async ({ setErrors, email }) => {
      setErrors?.([])
      try {
        return await axios.post('/forgot-password', { email })
      } catch (error) {
        throw error
      }
    },
    onError: (error, variables) => {
      if (error.response?.status === 422) {
        variables.setErrors?.(Object.values(error.response.data.errors || {}).flat())
      } else {
        throw error
      }
    }
  })

  const resetPassword = useMutation({
    mutationFn: async ({ setErrors, ...props }) => {
      setErrors?.([])
      try {
        return await axios.post('/reset-password', { token: params.token, ...props })
      } catch (error) {
        throw error
      }
    },
    onError: (error, variables) => {
      if (error.response?.status === 422) {
        variables.setErrors?.(Object.values(error.response.data.errors || {}).flat())
      } else {
        throw error
      }
    },
    onSuccess: () => navigate(`/login/${btoa('Password reset successfully')}`)
  })

  const removeTwoFactor = useMutation({
    mutationFn: async ({ setErrors, setStatus, setPending, ...props }) => {
      try {
        setStatus(null)
        setPending(true)
        await axios.post('/api/2fa/remove')
      } catch (error) {
        throw error
      }
    },
    onError: (error, { setErrors }) => setErrors(Object.values(error.response.data.errors).flat()),
    onSuccess: invalidateUserQuery
  })

  const activateTwoFactor = useMutation({
    mutationFn: async ({ setErrors, setStatus, setPending, ...props }) => {
      try {
        setStatus(null)
        setPending(true)
        await axios.post('/user/two-factor-authentication', props)
      } catch (error) {
        throw error
      }
    },
    onError: (error, { setErrors }) => setErrors(Object.values(error.response.data.errors).flat()),
    onSuccess: invalidateUserQuery
  })

  const verifyTwoFactorActivation = useMutation({
    mutationFn: async ({ setErrors, setStatus, setPending, ...props }) => {
      try {
        setStatus(null)
        setPending(true)
        await axios.post('/user/confirmed-two-factor-authentication', props)
      } catch (error) {
        throw error
      }
    },
    onError: (error, { setErrors }) => setErrors(Object.values(error.response.data.errors).flat()),
    onSuccess: invalidateUserQuery
  })

  const activateSmsTwoFactor = useMutation({
    mutationFn: async ({ setErrors, setStatus, setPending, ...props }) => {
      try {
        setStatus(null)
        setPending(true)
        await axios.post('/api/2fa/setup-sms', props)
      } catch (error) {
        throw error
      }
    },
    onError: (error, { setErrors }) => setErrors(Object.values(error.response.data.errors).flat()),
    onSuccess: invalidateUserQuery
  })

  const verifySmsTwoFactor = useMutation({
    mutationFn: async ({ setErrors, setStatus, setPending, ...props }) => {
      try {
        setStatus(null)
        setPending(true)
        await axios.post('/api/2fa/verify-setup', props)
      } catch (error) {
        throw error
      }
    },
    onError: (error, { setErrors }) => setErrors(Object.values(error.response.data.errors).flat()),
    onSuccess: invalidateUserQuery
  })

  const sendSmsTwoFactorCode = useMutation({
    mutationFn: async ({ setErrors, setStatus, setPending, ...props }) => {
      try {
        setStatus(null)
        setPending(true)
        await axios.post('/api/2fa/send', { 'verify_method': 'verify_sms' })
      } catch (error) {
        throw error
      }
    },
    onError: (error, { setErrors }) => setErrors(Object.values(error.response.data.errors).flat()),
    onSettled: (_, __, { setPending }) => setPending(false), 
    onSuccess: () => invalidateUserQuery()
  })

  const confirmSmsTwoFactorChallenge = useMutation({
    mutationFn: async ({ setErrors, setStatus, setPending, ...props }) => {
      try {
        setStatus(null)
        setPending(true)
        await axios.post('/api/2fa/verify', props)
      } catch (error) {
        throw error
      }
    },
    onError: (error, { setErrors }) => setErrors(Object.values(error.response.data.errors).flat()),
    onSettled: (_, __, { setPending }) => setPending(false), 
    onSuccess: () => invalidateUserQuery('/dashboard')
  })

  const confirmTwoFactorChallenge = useMutation({
    mutationFn: async ({ setErrors, setStatus, setPending, ...props }) => {
      try {
        setStatus(null)
        setPending(true)
        await axios.post('/two-factor-challenge', props)
      } catch (error) {
        throw error
      }
    },
    onError: (error, { setErrors }) => setErrors(Object.values(error.response.data.errors).flat()),
    onSettled: (_, __, { setPending }) => setPending(false), 
    onSuccess: invalidateUserQuery
  })

  const resendEmailVerification = useMutation({
    mutationFn: async ({ setErrors, setStatus, setPending, ...props }) => {
      try {
        setStatus(null)
        setPending(true)
        await axios.post('/email/verification-notification')
      } catch (error) {
        throw error
      }
    },
    onError: (error, { setErrors }) => setErrors(Object.values(error.response.data.errors).flat())
  })

  const logout = async () => {
    if (!error) {
      await axios.post('/logout')
      invalidateUserQuery()
    }
    window.location.pathname = '/login'
  }

  return {
    user,
    isFetched,
    checkPermission: (permission) => user?.all_permissions.includes(permission),
    checkRole: (role) => user?.all_roles.includes(role),
    register: register.mutate,
    login: login.mutate,
    forgotPassword: forgotPassword.mutate,
    resetPassword: resetPassword.mutate,
    removeTwoFactor: removeTwoFactor.mutate,
    activateTwoFactor: activateTwoFactor.mutate,
    verifyTwoFactorActivation: verifyTwoFactorActivation.mutate,
    activateSmsTwoFactor: activateSmsTwoFactor.mutate,
    verifySmsTwoFactor: verifySmsTwoFactor.mutate,
    sendSmsTwoFactorCode: sendSmsTwoFactorCode.mutate,
    confirmSmsTwoFactorChallenge: confirmSmsTwoFactorChallenge.mutate,
    confirmTwoFactorChallenge: confirmTwoFactorChallenge.mutate,
    resendEmailVerification: resendEmailVerification.mutate,
    logout
  }
}

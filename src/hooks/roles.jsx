import axios from '/src/lib/axios'
import { useSnackbar } from './snackbar'

export const useRoles = () => {
  let { addMessage } = useSnackbar()

  const createRole = async ({name, onComplete = () => {} }) => {
    axios.post('/api/roles', {name}).then(res => {
      onComplete(res)
      addMessage('New role created.')
    }).catch(error => {
      if (error.response.status !== 422) {
        throw error
      }
      addMessage(Object.values(error.response.data.message).flat(), 'error', null, 5000)
    })
  }

  const updateRole = async ({roleId, permissionIds, onComplete = () => {} }) => {
    axios.put(`/api/roles/${roleId}`, {permissionIds}).then(res => {
      onComplete(res)
      addMessage('Role updated.')
    }).catch(error => {
      if (error.response.status !== 422) {
        throw error
      }
      addMessage(Object.values(error.response.data.message).flat(), 'error', null, 5000)
    })

  }

  const createPermission = async ({name, onComplete = () => {} }) => {
    axios.post('/api/permissions', {name}).then(res => {
      onComplete(res)
      addMessage('New permission created.')
    }).catch(error => {
      if (error.response.status !== 422) {
        throw error
      }
      addMessage(Object.values(error.response.data.message).flat(), 'error', null, 5000)
    })
  }

  return {
    createRole,
    updateRole,
    createPermission
  }
}

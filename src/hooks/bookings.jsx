import { useMutation, useQueryClient } from '@tanstack/react-query'
import axios from '/src/lib/axios'
import { useSnackbar } from './snackbar'

export const useBookings = () => {
  const { addMessage } = useSnackbar()
  const queryClient = useQueryClient()

  const createBooking = useMutation({
    mutationFn: props => axios.post('/api/bookings', props),
    onSuccess: (data, variables) => {
      addMessage('Booking created!')
      if (variables.onComplete) variables.onComplete(data)
    },
    onError: error => {
      if (error.response?.status !== 422) {
        throw error
      }
      addMessage(Object.values(error.response.data.message).flat(), 'error', null, 5000)
    }
  })

  const updateBooking = useMutation({
    mutationFn: ({ bookingID, ...props }) => axios.put(`/api/bookings/${bookingID}`, props),
    onSuccess: (data, variables) => {
      addMessage('Booking updated!')
      queryClient.invalidateQueries({ queryKey: [`/api/bookings/${variables.bookingID}`] })
      if (variables.onComplete) variables.onComplete(data)
    },
    onError: error => {
      if (error.response?.status !== 422) {
        throw error
      }
      addMessage(Object.values(error.response.data.message).flat(), 'error', null, 5000)
    }
  })

  const deleteBooking = useMutation({
    mutationFn: ({bookingID}) => axios.delete(`/api/bookings/${bookingID}`),
    onSuccess: () => {
      addMessage('Booking deleted!')
    },
    onError: error => {
      if (error.response?.status !== 422) {
        throw error
      }
      addMessage(Object.values(error.response.data.message).flat(), 'error', null, 5000)
    }
  })

  const startDeliveries = useMutation({
    mutationFn: ({bookingID}) => axios.post(`/api/bookings/${bookingID}/start-transfers`),
    onSuccess: (data, variables) => {
      addMessage('Deliveries dispatched!')
      if (variables.onComplete) variables.onComplete(data)
    },
    onError: error => {
      if (error.response?.status !== 422) {
        throw error
      }
      addMessage(Object.values(error.response.data.message).flat(), 'error', null, 5000)
    }
  })

  return {
    createBooking: createBooking.mutate,
    updateBooking: updateBooking.mutate,
    deleteBooking: deleteBooking.mutate,
    startDeliveries: startDeliveries.mutate
  }
}

import axios from '/src/lib/axios'
import { useNavigate } from 'react-router-dom'
import { useSnackbar } from './snackbar'
import { useMutation, useQueryClient } from '@tanstack/react-query'

export const useSites = () => {
  const navigate = useNavigate()
  const { addMessage } = useSnackbar()
  const queryClient = useQueryClient()

  const createSite = useMutation({
    mutationFn: (props) => axios.post('/api/cinemas', props),
    onSuccess: (res) => {
      addMessage('Site created!')
      navigate(`/sites/${res.data.data.id}`)
    },
    onError: (error, props) => {
      if (error.response.status === 422) {
        props.setErrors(Object.values(error.response.data.errors).flat())
      } else {
        throw error
      }
    }
  })

  const createContact = useMutation({
    mutationFn: ({ siteID, ...props }) => axios.post(`/api/cinemas/${siteID}/contacts`, props),
    onSuccess: (res, variables) => {
      addMessage('Contact created!')
      queryClient.invalidateQueries({ queryKey: [`/api/cinemas/${variables.siteID}`]})
    },
    onError: () => {
      addMessage('Could not create a contact!', 'error')
    }
  })

  const updateSite = useMutation({
    mutationFn: ({ siteID, ...props }) => axios.put(`/api/cinemas/${siteID}`, props),
    onSuccess: () => {
      addMessage('Site updated!')
    },
    onError: (error, props) => {
      if (error.response.status === 422) {
        props.setErrors(Object.values(error.response.data.errors).flat())
      } else {
        throw error
      }
    }
  })

  const deleteSite = useMutation({
    mutationFn: ({ siteID }) => axios.delete(`/api/cinemas/${siteID}`),
    onSuccess: () => {
      addMessage('Site deleted!')
    },
    onError: () => {
      addMessage('Could not delete this site.', 'error')
    }
  })

  const deleteContact = useMutation({
    mutationFn: ({ siteID, contactID }) => axios.delete(`/api/cinemas/${siteID}/contacts/${contactID}`),
    onSuccess: (res, variables) => {
      addMessage('Contact deleted!')
      queryClient.invalidateQueries({ queryKey: [`/api/cinemas/${variables.siteID}`]})
    },
    onError: () => {
      addMessage('Could not delete a contact!', 'error')
    }
  })

  return {
    createSite: createSite.mutate,
    createContact: createContact.mutate,
    updateSite: updateSite.mutate,
    deleteSite: deleteSite.mutate,
    deleteContact: deleteContact.mutate
  }
}

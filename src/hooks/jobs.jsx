import React from 'react'
import axios from '/src/lib/axios'
import { DeliveryContext } from '/src/contexts/delivery'
import { useSnackbar } from './snackbar'

export const useJobs = () => {
  let { addMessage } = useSnackbar()

  const createJob = async ({ setErrors, onComplete, ...props }) => {
    setErrors([])
    axios.post('/api/media-managers/jobs', props).then(res => {
      addMessage('Job submitted!')
      onComplete(res)
    }).catch(error => {
      if (error.response.status !== 422) {
        throw error
      }
      addMessage(error.response.data.message, 'error', 'alert', 5000)
    })
  }

  const retryJob = async ({ jobID }) => {
    axios.post(`/api/media-managers/retry-job/${jobID}`).then(res => {
      addMessage('Job re-submitted!')
    }).catch(error => {
      if (error.response.status !== 422) {
        throw error
      }
      addMessage(error.response.data.message, 'error', 'alert', 5000)
    })
  }

  const deleteJob = async ({ jobID }) => {
    axios.delete(`/api/media-managers/jobs/${jobID}`).then(res => {
      addMessage('Job deleted!')
    }).catch(error => {
      if (error.response.status !== 422) {
        throw error
      }
      addMessage(error.response.data.message, 'error', 'alert', 5000)
    })
  }

  const [, dispatch] = React.useContext(DeliveryContext)

  const showJob = job => {
    dispatch({ job })
  }

  return {
    createJob,
    retryJob,
    deleteJob,
    showJob
  }
}

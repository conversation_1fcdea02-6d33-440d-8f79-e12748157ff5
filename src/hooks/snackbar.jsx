import React from 'react'
import { SnackbarContext } from '/src/contexts/snackbar'

export const useSnackbar = () => {

  const [, dispatch] = React.useContext(SnackbarContext)

  const addMessage = (text, type = 'success', icon, timeout = 2000, showCloseButton = false) => {
    const _id = Math.random().toString(36).substring(7)
    const message = {
      _id,
      message: text,
      icon,
      type,
      timeout,
      showCloseButton,
      onClose: () => removeMessage(_id)
    }
    dispatch({ type: 'add_message', message })
  }

  const removeMessage = messageID => {
    dispatch({ type: 'remove_message', messageID })
  }

  return {
    addMessage
  }
}

import axios from '/src/lib/axios'
import { useNavigate } from 'react-router-dom'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useAuth } from './auth'
import { useSnackbar } from './snackbar'

export const useUsers = () => {
  const navigate = useNavigate()
  const { addMessage } = useSnackbar()
  const { user } = useAuth()
  const queryClient = useQueryClient()

  const createUser = useMutation({
    mutationFn: (props) => axios.post('/api/users', props),
    onSuccess: (res, variables) => {
      addMessage('User created!')
      navigate(res.data.data.id)
      if (typeof variables.onComplete === 'function') {
        variables.onComplete()
      }
      queryClient.invalidateQueries({ queryKey: ['/api/users'] }) 
    },
    onError: (error, variables) => {
      if (error.response.status === 422) {
        variables.setErrors(Object.values(error.response.data.errors).flat())
      } else {
        throw error
      }
    }
  })

  const updateUser = useMutation({
    mutationFn: ({ userID, ...props }) => axios.put(`/api/users/${userID}`, props),
    onSuccess: (res, variables) => {
      if (variables.userID === user.id) {
        queryClient.invalidateQueries(['/api/user'])
      } else {
        queryClient.invalidateQueries(['/api/users'])
      }
      addMessage('User updated!')
      if (typeof variables.onComplete === 'function') {
        variables.onComplete(res)
      }
    },
    onError: (error, variables) => {
      if (error.response.status === 422) {
        variables.setErrors(Object.values(error.response.data.errors).flat())
      } else {
        throw error
      }
      if (typeof variables.onComplete === 'function') {
        variables.onComplete()
      }
    }
  })

  const deleteUser = useMutation({
    mutationFn: ({ userID }) => axios.delete(`/api/users/${userID}`),
    onSuccess: () => {
      addMessage('User deleted!')
      queryClient.invalidateQueries(['/api/users']) 
      navigate('/users')
    },
    onError: (error, variables) => {
      if (error.response.status === 422) {
        variables.setErrors(Object.values(error.response.data.errors).flat())
      } else {
        throw error
      }
    }
  })

  const createToken = useMutation({
    mutationFn: ({ userID, ...props }) => axios.post(`/api/users/${userID}/tokens`, props),
    onSuccess: (res, variables) => {
      addMessage('Token created!')
      if (typeof variables.onComplete === 'function') {
        variables.onComplete(res.data)
      }
    },
    onError: (error, variables) => {
      if (error.response.status === 422) {
        variables.setErrors(Object.values(error.response.data.errors).flat())
      } else {
        throw error
      }
    }
  })

  const deleteToken = useMutation({
    mutationFn: ({ userID, tokenID }) => axios.delete(`/api/users/${userID}/tokens/${tokenID}`),
    onSuccess: (res, variables) => {
      addMessage('Token deleted!')
      if (typeof variables.onComplete === 'function') {
        variables.onComplete()
      }
    },
    onError: (error, variables) => {
      if (error.response.status === 422) {
        variables.setErrors(Object.values(error.response.data.errors).flat())
      } else {
        throw error
      }
    }
  })

  const resendInvite = useMutation({
    mutationFn: (userID) => axios.get(`/api/users/${userID}/resend`),
    onSuccess: () => {
      addMessage('Invite email has been sent.')
    },
    onError: (error, variables) => {
      if (error.response.status === 422) {
        variables.setErrors(Object.values(error.response.data.errors).flat())
      } else {
        throw error
      }
    }
  })

  return {
    createUser: createUser.mutate,
    updateUser: updateUser.mutate,
    deleteUser: deleteUser.mutate,
    resendInvite: resendInvite.mutate,
    createToken: createToken.mutate,
    deleteToken: deleteToken.mutate
  }
}

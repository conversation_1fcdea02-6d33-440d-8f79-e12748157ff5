import { Routes, Route, Navigate, useLocation } from 'react-router-dom'
import Dashboard from '/src/pages/dashboard'
import Login from '/src/pages/login'
import TwoFactorChallenge from '/src/pages/two-factor-challenge'
import Home from '/src/pages/home'
import ForgotPassword from '/src/pages/forgot-password'
import PasswordReset from '/src/pages/password-reset'
import { useAuth } from '/src/hooks/auth'
import Navigation from '/src/components/navigation'
import Snackbar from '/src/components/snackbar'
import Forbidden from '/src/pages/errors/forbidden'
import Sites from '/src/pages/sites'
import Site from '/src/pages/site'
import Titles from '/src/pages/titles'
import Title from '/src/pages/title'
import Bookings from '/src/pages/bookings'
import Booking from '/src/pages/booking'
import Transfers from '/src/pages/transfers'
import DeliverySlide from '/src/components/transfers/slide'
import Organizations from '/src/pages/organizations'
import Organization from '/src/pages/organization'
import Users from '/src/pages/users'
import User from '/src/pages/user'
import Settings from '/src/pages/settings'
import Reports from '/src/pages/reports'
import Support from '/src/pages/support'
import { Status } from '@bitcine/cinesend-theme'
import { SnackbarProvider } from '/src/contexts/snackbar'
import { DeliveryProvider } from '/src/contexts/delivery'
import VerifySms from '/src/pages/verify-sms'
import VerifyEmail from '/src/pages/verify-email'
import LiveStreamDecoderPage from './pages/equipment/livestream_decoder'
import CinemaProPage from './pages/equipment/cinema_pro'
import ImportRecord from './pages/import'
import Imports from './pages/imports'
import Roles from './pages/roles'
import PublicUpload from './pages/upload'
import Equipment from './pages/equipment'
import ServerPage from './pages/equipment/details'
import Releases from './pages/releases'
import Release from './pages/release'
import SatelliteQueue from './pages/satellite'

function PublicRoutes() {
  return (
    <Routes>
      <Route path='/' element={<Navigate to='/login' replace />} />
      <Route path='login' element={<Login />} />
      <Route path='login/:reset' element={<Login />} />
      <Route path='two-factor-challenge' element={<TwoFactorChallenge />} />
      <Route path='verify' element={<VerifyEmail />} />
      <Route path='forgot-password' element={<ForgotPassword />} />
      <Route path='password-reset/:token/:email' element={<PasswordReset />} />
      <Route path='upload' element={<PublicUpload />} />
      <Route path='*' element={<Home />} />
    </Routes>
  )
}

function PrivateRoutes({ user }) {
  const isVerifying2FA = user.two_factor_activated && (user.two_factor_pending || user.two_factor_sms_pending)
  const pathname = useLocation().pathname.split('/')[1]
  const publicRoutes = ['upload']
  const isNotPublicRoute = publicRoutes.includes(pathname)
  const showNavigationAndSlide = !isVerifying2FA && !isNotPublicRoute
  return (
    <DeliveryProvider>
      <div className=''>
        <div className='flex h-screen w-screen font-content'>
          {showNavigationAndSlide && (
            <>
              <Navigation />
              <DeliverySlide />
            </>
          )}
          <Routes>
            <Route path='dashboard' element={<Dashboard />} />
            <Route path='titles' element={<Titles />} />
            <Route path='titles/:titleID/*' element={<Title />} />
            <Route exact path='releases' element={<Navigate to={'/releases/all'} />} />
            <Route path='releases/*' element={<Releases />} />
            <Route path='release/:releaseID/*' element={<Release />} />
            <Route path='sites' element={<Sites />} />
            <Route path='satellite' replace element={<Navigate to={'/satellite/active'}/>}/>
            <Route path='satellite/*' element={<SatelliteQueue/>}/>
            <Route path='sites/:siteID/*' element={<Site />} />
            <Route path='bookings' element={<Bookings />} />
            <Route path='bookings/:bookingID/*' element={<Booking />} />
            <Route path='imports' element={<Imports />} />
            <Route path='imports/:importID/*' element={<ImportRecord />} />
            <Route path='transfers' element={<Transfers />} />
            <Route path='equipment/*' element={<Equipment />} />
            <Route path='server/:deviceID/*' element={<ServerPage />} />
            <Route path='livestream-decoder/:deviceID/*' element={<LiveStreamDecoderPage />} />
            <Route path='cinema-pros/:deviceID/*' element={<CinemaProPage />} />
            <Route path='organizations' element={<Organizations />} />
            <Route path='organizations/:organizationID/*' element={<Organization />} />
            <Route path='users' element={<Users />} />
            <Route path='users/:userID/*' element={<User />} />
            <Route path='reports' element={<Reports />} />
            <Route path='support' element={<Support />} />
            <Route path='roles' element={<Roles />} />
            <Route path='settings/*' element={<Settings />} />
            <Route path='forbidden' element={<Forbidden />} />
            <Route path='verify' element={<VerifySms />} />
            <Route path='upload' element={<PublicUpload />} />
            <Route path='*' element={<Navigate to='/dashboard' replace />} />
          </Routes>
        </div>
      </div>
    </DeliveryProvider>
  )
}

function App() {
  const { user, isFetched } = useAuth({ middleware: 'guest' })

  return (
    <SnackbarProvider>
      <Status pending={!isFetched} height={'h-screen'}>
        {user ? <PrivateRoutes user={user} /> : <PublicRoutes />}
        <Snackbar />
      </Status>
    </SnackbarProvider>
  )
}

export default App

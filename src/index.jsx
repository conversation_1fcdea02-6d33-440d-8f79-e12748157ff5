import React from 'react'
import { createRoot } from 'react-dom/client'
import { <PERSON><PERSON>er<PERSON>out<PERSON> } from 'react-router-dom'
import App from './App'
import dayjs from 'dayjs'
import advancedFormat from 'dayjs/plugin/advancedFormat'
import dayjsPluginUTC from 'dayjs-plugin-utc'
import relativeTime from 'dayjs/plugin/relativeTime'
import timezone from 'dayjs/plugin/timezone'
import localizedFormat from 'dayjs/plugin/localizedFormat'
import {
  QueryClient,
  QueryClientProvider
} from '@tanstack/react-query'
import './index.css'
import axios from '/src/lib/axios'

dayjs.extend(relativeTime)
dayjs.extend(advancedFormat)
dayjs.extend(dayjsPluginUTC)
dayjs.extend(timezone)
dayjs.extend(localizedFormat)

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: async ({ queryKey }) => {
        const { data } = await axios.get(queryKey.join(''))
        return data
      }
    }
  }
})

const rootElement = document.getElementById('root')
const root = createRoot(rootElement)

root.render(
  <React.StrictMode>
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <App />
      </QueryClientProvider>
    </BrowserRouter>
  </React.StrictMode>
)
@import '@bitcine/cinesend-theme/dist/styles/fonts/clash-display/css/clash-display.css';
@import '@bitcine/cinesend-theme/dist/styles/fonts/clash-grotesk/css/clash-grotesk.css';
@import '@bitcine/cinesend-theme/dist/styles/fonts/inter/inter.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    background: #f9fafb;
    margin: 0;
    @apply font-content;
  }
  h1,h2,h3,h4,h5,h6 {
    @apply font-header;
  }
  h1 {
    @apply text-[64px] !important;
    @apply leading-[80px] !important;
  }
  h2 {
    @apply text-[48px] !important;
    @apply leading-[64px] !important;
  }
  h3 {
    @apply text-[40px] !important;
    @apply leading-[48px] !important;
  }
  h4 {
    @apply text-[32px] !important;
    @apply leading-[40px] !important;
  }
  h5 {
    @apply text-[18px] !important;
    @apply leading-[24px] !important;
  }
  h6 {
    @apply text-base;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-header;
  }
  svg {
    @apply inline-flex;
  }
}

.card-box {
  @apply bg-white border border-[#EDEDFB] rounded-lg drop-shadow-[0_4px_6px_rgba(0,0,0,0.15)] p-6
}

.success-delivery-step {
  @apply bg-green-100 text-green-600 border-green-200;
}

.in-progress-delivery-step {
  @apply bg-blue-100 text-blue-600 border-blue-200;
}

.none-delivery-step {
  @apply bg-gray-100 text-gray-600 border-gray-200;
}

.pending-delivery-step {
  @apply bg-yellow-100 text-yellow-600 border-yellow-200;
}
